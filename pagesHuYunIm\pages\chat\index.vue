<template>
  <view :id="page_font_size">
    <view class="flex_c page" @touchmove="touchmove">
      <view class="navigationRef">
        <navigation ref="navigationRef" :groupCount="groupCount" :title="pagueObj.name" :group_id="pagueObj.id"></navigation>
      </view>
      <scroll-view
        class="flex1 scroll-Y"
        @tap.stop="onPage"
        id="scroll-view"
        lower-threshold="100"
        scroll-y
        scroll-with-animation
        :scroll-top="scroll_top"
        @scroll="scroll"
        @scrolltoupper="scrolltoupper"
        @scrolltolower="scrolltolower"
      >
        <view class="scroll-view-str" :style="{ height: `${reserveHeight}px` }" v-if="reserveHeight > 0"></view>

        <view class="messageList_">
          <template v-for="(item, index) in list">
            <!-- #ifdef APP || H5 -->
            <view
              class="z_index2"
              :class="`oneheight_${index}`"
              :key="item.id + index"
              v-if="!item.isHide"
            >
              <view class="icon_ text_26 color__ time">
                {{ renderMessageDate(item, index) }}
              </view>
              <view :key="item.messageId + index" v-if="!item.recalled">
                <item
                  :isMy="isSelf(item.senderId)"
                  :myid="myid"
                  :item="item"
                  @onClick="onItem"
                  @onLongpress="onLongpress"
                  @mention="mention"
                  @imgLoad="imgLoad"
                ></item>
              </view>
              <view class="icon_ text_26 recalled" v-else>
                <view class="">
                  <text v-if="isSelf(item.senderId)">你</text>
                  <text v-else>{{ item.senderData.name }}</text>
                  撤回了一条消息
                </view>
                <view class="recalled-edit" v-if="item.type === 'text' && isSelf(item.senderId)" @click="recalledEdit(item)">重新编辑</view>
              </view>
            </view>
            <!-- #endif -->
            <!-- #ifdef MP -->
            <view class="z_index2" :key="item.id" v-if="!item.isHide">
              <view class="icon_ text_26 color__ time">
                {{ renderMessageDate(item, index) }}
              </view>
              <view :key="item.id" v-if="!item.recalled">
                <item
                  :isMy="isSelf(item.userId)"
                  :myid="myid"
                  :item="item"
                  @onClick="onItem"
                  @onLongpress="onLongpress"
                  @mention="mention"
                ></item>
              </view>
              <view class="icon_ text_26 recalled" v-else>
                <view class="">
                  <text v-if="isSelf(item.senderId)">你</text>
                  <text v-else>{{ item.senderData.name }}</text>
                  撤回了一条消息
                </view>
                <view class="recalled-edit" v-if="item.type === 'text' && isSelf(item.senderId)" @click="recalledEdit(item)">重新编辑</view>
              </view>
            </view>
            <!-- #endif -->
          </template>
        </view>
        <view :style="{ height: $store.state.StatusBar.customBar - 8 + 4 + 'px' }"></view>
      </scroll-view>
      <view class="bottomOperationRef">
        <bottom-operation
          ref="bottomOperationRef"
          :to="to"
          :userList="userList"
          @pushList="pushList"
          @onBottom="onBottom"
          @backToBottom="bottomOperationScrollToBottom"
          @focus="focus"
          @keyboardheightchange="keyboardheightchange"
        ></bottom-operation>
      </view>
    </view>
    <!-- 视频播放器 -->
    <video-player-ref
      v-model="videoPlayer.show"
      :url="videoPlayer.url"
      @onVideoFullScreenChange="onVideoFullScreenChange"
    ></video-player-ref>
    <!-- 复制；撤回等操作 -->
    <operate ref="operateRef" @quote="quote" @thank="thank" @transmit="transmit"></operate>
    <!-- 转发选择聊天 -->
    <m-group-selection ref="groupSelectionRef" @sendMessage="sendMessage"></m-group-selection>
  </view>
</template>
<script>
import { 自己的信息, 对话数据 } from '@/TEST/index.js'
import navigation from './components/navigation/index.vue'
import bottomOperation from './components/bottom-operation/index.vue'
import item from './components/item/index'
import videoPlayerRef from './components/video-player/index'
import openRedPacket from './components/open-red-packet/index'
import operate from './components/operate/index'
import { mapState } from 'vuex'
import { msglist } from '../../api/public'
import mqttClient from '../../utils/mqttClient.js'
import mqtt from '../../lib/mqtt.min.js'
import store from '../../store/index.js'
import { SplitArray } from '../../utils'
import { show, formatDate, throttle, openimg, getLocation, to as tofn } from '@/utils/index.js'
import { createUserInfo } from '../../utils/mqttConfig.js'

let lastMessageTimeStamp = null

let envelopeClickList = []
let innerAudioContext = uni.createInnerAudioContext()
let audioItem = {}
let group = {}

let groupId = null

// 浏览照片数组
let imageList = []

// 是否是手动触发的列表滑动
let isBottomOperationScrollToBottom = false

const IMAGE_MAX_WIDTH = 200
const IMAGE_MAX_HEIGHT = 150
let scroll_top = 0
let reserveHeightRef = 0
let bottomOperationRefHeight = 0
export default {
  components: {
    // groupSelection,
    navigation,
    bottomOperation,
    item,
    videoPlayerRef,
    openRedPacket,
    operate
  },
  name: 'groupChat',
  data() {
    return {
      isHistoryGet: false,
      reserveHeight: 0,
      keyboardheightchangeValue: 0,
      myid: null,
      scroll_top,
      userList: [], //群成员列表
      groupCount: '',
      pagueObj: {
        name: '饭搭子5人组'
      },
      to: {},
      // 历史数据
      history: {
        messages: [],
        allLoaded: false
      },
      videoPlayer: {
        show: false,
        url: '',
        context: null
      },
      // 添加缺失的数据属性
      page: 1,
      pageSize: 50,
      groupId: '',
      loading: false,
      loadend: false,
      list: [],
      userMap: {},
      mqttClient: null,
      mqttPingInterval: null,
      groupIdNew: 0,
      userArr: [], //群成员
      groupInfo: {}, //群信息
      userInfo: {} //用户信息
    }
  },
  async onLoad(e) {
    console.log('🚀 ~ onLoad ~ e:', e)
    const groupInfo = JSON.parse(decodeURIComponent(e.groupInfo))
    this.groupInfo = groupInfo
    this.userInfo = JSON.parse(decodeURIComponent(e.userInfo))
    this.userArr = groupInfo.userArr
    // 设置MQTT库
    mqttClient.setMqttLib(mqtt)
    this.groupIdNew = groupInfo.id
    //
    scroll_top = 0
    this.scroll_top = scroll_top
    imageList = []
    envelopeClickList = uni.getStorageSync('envelopeClickList') || []
    lastMessageTimeStamp = e.lastMessageTimeStamp || null
    this.isHistoryGet = e.lastMessageTimeStamp
    groupId = groupInfo.id
    //
    this.myid = this.userInfo.userId
    this.initMqtt()
    this.loadHistoryMessage()
    this.$nextTick(() => {
      let view = uni.createSelectorQuery().select('.bottomOperationRef')
      view
        .boundingClientRect((ref) => {
          bottomOperationRefHeight = ref.height
        })
        .exec()
    })
  },
  onPageScroll() {
    this.$refs.bottomOperationRef.closeAll()
  },
  onReady() {
    this.videoPlayer.context = uni.createVideoContext('videoPlayer', this)
  },

  onUnload() {
    // 页面卸载时清理资源
    mqttClient.disconnect()
    // 清理音频资源
    if (innerAudioContext) {
      try {
        innerAudioContext.destroy()
      } catch (error) {
        console.warn('清理音频资源失败:', error)
      }
    }
  },
  computed: mapState({
    page_font_size: (state) => state.page_font_size,
    //显示时间
    renderMessageDate() {
      return (message, index) => {
        // 检查 createTime 是否存在
        if (!message.createTime) {
          return ''
        }
        //正则替换 -
        const createTimeStamp = new Date(message.createTime.replace(/-/g, '/')).getTime()

        // 第一条消息总是显示时间
        if (index === 0) {
          return message.createTime
        }

        // 获取前一条消息
        const prevMessage = this.list[index - 1]
        if (prevMessage && prevMessage.createTime) {
          const prevCreateTimeStamp = new Date(prevMessage.createTime.replace(/-/g, '/')).getTime()
          // 如果当前消息比前一条消息晚3分钟以上，则显示时间
          if (createTimeStamp - prevCreateTimeStamp > 3 * 60 * 1000) {
            return message.createTime
          }
        }

        return ''
      }
    },
    // 是否本人isMy
    isSelf() {
      return (senderId) => {
        const { userId = '' } = this.userInfo
        return senderId === `${userId}`
      }
    },
    envelope_top_opened() {
      return (id) => {
        return this.envelopeXollectionList.includes(id)
      }
    }
  }),

  methods: {
    /**
     * 加载消息数据
     * @param {string} idEnd - 结束ID，用于分页加载
     * @returns {Promise<void>}
     */
    async loadHistoryMessage(idEnd = '') {
      // 防止重复加载和已加载完成的情况
      if (this.loading || this.loadend) {
        return
      }
      try {
        this.loading = true

        // 构建请求参数
        const requestData = {
          page: this.page,
          pageSize: this.pageSize,
          groupId: this.groupIdNew,
          ...(idEnd && { id_end: idEnd })
        }

        const [res, err] = await msglist(requestData)

        if (err) {
          console.error('加载消息数据失败:', err)
          uni.showToast({
            title: '加载失败，请重试',
            icon: 'none'
          })
          return
        }

        if (res && Array.isArray(res)) {
          // 处理语音消息内容
          this.processMessages(res)

          // 确保消息按时间正序排列（最早的在前，最新的在后）
          res.sort((a, b) => {
            const timeA = new Date(a.createTime).getTime()
            const timeB = new Date(b.createTime).getTime()
            return timeA - timeB
          })

          // 合并数据到列表
          if (idEnd) {
            // 分页加载：将历史消息添加到列表开头
            this.list = SplitArray(res, []).concat(this.list)
          } else {
            // 初始加载：直接设置列表
            this.list = SplitArray(res, this.list)
          }
          // 检查是否已加载完所有数据
          this.loadend = res.length < this.pageSize
          console.log('消息数据加载成功:', res)
          // 如果不是分页加载，滚动到底部
          if (!idEnd) {
            this.$nextTick(() => {
              this.getHeight()
            })
          }
        }
      } catch (error) {
        console.error('loadData 异常:', error)
        uni.showToast({
          title: '网络异常，请重试',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    /**
     * 初始化MQTT连接
     */
    async initMqtt() {
      try {
        // 从store获取用户信息，如果没有则使用默认值
        const userInfo = createUserInfo(
          '1921822887908581377', // userId
          '范发发', // nickname
          'hbs119', // channelCode
          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImhiczExOSIsImNsaWVudGlkIjoiMTkyMTgyMjg4NzkwODU4MTM3NyIsImV4cCI6MTc1NTA1ODUyOX0.S24GcZb6K88dr6FdH82v9oqvTbBqVj9u9xOvd2b30_Y', // token
          'https://dummyimage.com/100x100/cccccc/ffffff?text=头像', // avatar
          'DEV' // 环境
        )
        // 创建用户信息对象
        const mqttUserInfo = createUserInfo(
          userInfo.userId,
          userInfo.nickname || '用户',
          userInfo.channelCode,
          userInfo.token,
          userInfo.avatar || this.defaultAvatar,
          'DEV'
        )

        const callbacks = {
          onConnect: () => {
            console.log('MQTT连接成功')
            this.isConnected = true
          },
          onMessage: (topic, mqttMsg) => {
            this.handleMqttMessage(mqttMsg)
          },
          onReconnect: () => {
            console.log('MQTT重连中...')
            this.isConnected = false
          },
          onError: (error) => {
            console.error('MQTT连接错误:', error)
            this.isConnected = false
          },
          onEnd: () => {
            console.log('MQTT连接已断开')
            this.isConnected = false
          }
        }

        // 连接MQTT
        mqttClient.connect(mqttUserInfo, callbacks)
      } catch (error) {
        console.error('初始化MQTT失败:', error)
        this.isConnected = false
      }
    },

    /**
     * 处理消息内容
     * @param {Array} messages - 消息列表
     */
    processMessages(messages) {
      messages.forEach((item) => {
        const findUser = this.userArr.find((user) => user.userId === item.userId)
        // 处理发送者数据
        if (findUser) {
          item.senderData = {
            name: findUser.nickname,
            avatar: findUser.avatar,
            member_id: findUser.userId,
            group_id: item.groupId
          }
        }
        // 处理语音消息内容
        if (item.msgType === 'voice' && typeof item.content === 'string' && item.content.length > 10) {
          try {
            item.payload = { text: JSON.parse(item.content) }
          } catch (error) {
            console.warn('解析语音消息内容失败:', error, item)
          }
        } else {
          item.payload = { text: item.content }
        }
      })
    },
    /**
     * 连接MQTT服务器
     * @returns {Promise<void>}
     */
    async connectMqtt() {
      try {
        // 检查必要的用户信息
        if (!store.state?.userInfo?.userId || !store.state?.userInfo?.wsUrl) {
          console.error('用户信息不完整，无法连接MQTT')
          return
        }

        const userInfo = store.state.app.userInfo

        // MQTT连接配置
        const mqttOptions = {
          protocolId: 'MQTT',
          protocolVersion: 4,
          clientId: userInfo.userId,
          username: userInfo.channelCode,
          password: store.state.app.token,
          clean: false,
          keepalive: 60,
          connectTimeout: 30 * 1000,
          reconnectPeriod: 1000
        }

        // 创建MQTT连接
        this.mqttClient = mqtt.connect(userInfo.wsUrl, mqttOptions)

        // 绑定事件监听器
        this.bindMqttEvents()
      } catch (error) {
        console.error('MQTT连接失败:', error)
        uni.showToast({
          title: 'MQTT连接失败',
          icon: 'none'
        })
      }
    },

    /**
     * 绑定MQTT事件监听器
     */
    bindMqttEvents() {
      if (!this.mqttClient) return

      const userInfo = store.state.app.userInfo

      // 连接成功事件
      this.mqttClient.on('connect', () => {
        console.log('MQTT连接成功')

        // 清除之前的心跳定时器
        this.clearPingInterval()

        // 设置心跳定时器
        this.mqttPingInterval = setInterval(() => {
          if (this.mqttClient && this.mqttClient.connected) {
            this.mqttClient.publish(`/chat/server/${userInfo.userId}/ping`, '1')
            console.log('MQTT心跳发送')
          }
        }, 10000)

        // 订阅用户消息频道
        this.mqttClient.subscribe(`/chat/client/${userInfo.userId}`, (err) => {
          if (err) {
            console.error('订阅消息频道失败:', err)
          } else {
            console.log('订阅消息频道成功')
          }
        })
      })

      // 重连事件
      this.mqttClient.on('reconnect', () => {
        console.log('MQTT重连中...')
      })

      // 错误事件
      this.mqttClient.on('error', (error) => {
        console.error('MQTT连接错误:', error)
      })

      // 连接结束事件
      this.mqttClient.on('end', () => {
        console.log('MQTT连接断开')
        this.clearPingInterval()
      })

      // 消息接收事件
      this.mqttClient.on('message', (topic, message) => {
        this.handleMqttMessage(topic, message)
      })
    },

    /**
     * 处理MQTT消息
     * @param {string} _topic - 消息主题（暂未使用）
     * @param {Buffer} message - 消息内容
     */
    handleMqttMessage(_topic, message) {
      try {
        const messageStr = message.toString()
        console.log('收到MQTT消息:', messageStr)

        const mqttMsg = JSON.parse(messageStr)

        if (mqttMsg.command === 'chatMsg') {
          this.processChatMessage(mqttMsg.data)
        }
      } catch (error) {
        console.error('处理MQTT消息失败:', error)
      }
    },

    /**
     * 处理聊天消息
     * @param {Object} chatMsg - 聊天消息数据
     */
    processChatMessage(chatMsg) {
      // 添加用户昵称
      if (this.userMap[chatMsg.userId]) {
        chatMsg.nickname = this.userMap[chatMsg.userId].nickname
      }

      if (this.groupId === chatMsg.groupId) {
        // 当前群组消息，添加到列表
        this.list.push(chatMsg)

        // 标记消息为已读
        this.markMessageAsRead(chatMsg.groupId)
      } else {
        // 其他群组消息，显示通知
        this.showMessageNotification(chatMsg)
      }
    },

    /**
     * 标记消息为已读
     * @param {string} groupId - 群组ID
     */
    markMessageAsRead(groupId) {
      if (this.mqttClient && this.mqttClient.connected) {
        const userInfo = store.state.app.userInfo
        const readMessage = JSON.stringify({ groupId })
        this.mqttClient.publish(`/chat/server/${userInfo.userId}/read`, readMessage)
      }
    },

    /**
     * 显示消息通知
     * @param {Object} chatMsg - 聊天消息
     */
    showMessageNotification(chatMsg) {
      try {
        if (this.$refs.notice) {
          this.$refs.notice.add({
            message: chatMsg.content,
            nickname: chatMsg.nickname
          })
        }
      } catch (error) {
        console.error('显示消息通知失败:', error)
      }
    },

    /**
     * 清除心跳定时器
     */
    clearPingInterval() {
      if (this.mqttPingInterval) {
        clearInterval(this.mqttPingInterval)
        this.mqttPingInterval = null
      }
    },

    /**
     * 断开MQTT连接
     */
    disconnectMqtt() {
      this.clearPingInterval()

      if (this.mqttClient) {
        this.mqttClient.end()
        this.mqttClient = null
      }
    },
    setHeight(e) {
      const res = uni.getSystemInfoSync()
      const windowHeight = res.windowHeight
      // 20 名字向上偏移
      // 8 内边距补偿
      // 4 名字偏移补偿
      const customBar = this.$store.state.StatusBar.customBar - 8 + 4
      const reserveHeight = windowHeight - e - customBar - bottomOperationRefHeight
      this.reserveHeight = reserveHeight
      reserveHeightRef = reserveHeight
    },

    getHeight(e) {
      this.$nextTick(() => {
        let view = uni.createSelectorQuery().select('.messageList_')
        view
          .boundingClientRect((select) => {
            if (!select) return
            if (!select?.height) {
              this.$nextTick(() => {
                let view2 = uni.createSelectorQuery().select('.messageList_')
                view2
                  .boundingClientRect((select) => {
                    this.setHeight(select.height)
                    if (e) {
                      setTimeout(() => {
                        this.reserveHeight = this.reserveHeight - this.keyboardheightchangeValue
                      })
                    }
                  })
                  .exec()
              })
            } else {
              this.setHeight(select.height)
              if (e) {
                setTimeout(() => {
                  this.reserveHeight = this.reserveHeight - this.keyboardheightchangeValue
                })
              }
            }
          })
          .exec()
      })
    },

    //图片加载完成
    imgLoad() {
      if (this.list.length > 20) return
      this.getHeight(true)
    },
    keyboardheightchange(e, e2 = false) {
      this.keyboardheightchangeValue = e
      if (reserveHeightRef) {
        this.reserveHeight = reserveHeightRef - e
      }
      if (e === 0) {
        if (e2) return
        this.getHeight()
      }
    },

    // 点击整个页面
    onPage() {
      this.$refs.bottomOperationRef.close()
      this.$refs.operateRef.close()
    },
    touchmove() {
      // this.$refs.bottomOperationRef.closeAll();
    },
    onBottom() {
      this.$refs.operateRef.close()
    },
    // 输入框获取焦点
    focus() {
      if (this.isHistoryGet) {
        this.isHistoryGet = false
        lastMessageTimeStamp = null
        this.list = []
        this.loadHistoryMessage()
      }
    },
    // 获取聊天记录
    // loadHistoryMessage() {
    //   uni.hideLoading()
    //   let list = JSON.parse(JSON.stringify(对话数据))
    //   list = list.reverse()
    //   // 同步混入数据
    //   list.forEach((im) => {
    //     // 缓存照片地址，
    //     if (im.type === 'image' || im.type === 'image_transmit') {
    //       imageList.unshift(im.payload.url)
    //     }
    //   })
    //   // 模拟只有少量数据
    //   // this.history.messages = [list[0],list[1],list[2]];
    //   this.history.messages = [...this.history.messages, ...list]
    //   if (this.history.messages.length > 20) return
    //   this.$nextTick(() => {
    //     this.getHeight()
    //   })
    // },
    onMessageReceived(message) {
      if (message.groupId === group.id) {
        // push进列表
        this.pushList(message)
        //聊天时，收到消息标记为已读
        this.markGroupMessageAsRead()
      }
    },
    // 转发成功后
    sendMessage(message) {
      // push进列表
      if (message.groupId === groupId) {
        this.pushList(message)
        // 同步消息到首页
        uni.$emit('onMessageReceived', message)
      }
    },
    // 将信息设置为已读
    markGroupMessageAsRead() {
      //
    },
    // 组装item
    initMessageItem(message, index) {
      message['isHide'] = 0
      // 初始化语音
      if (message.type === 'audio') {
        message['pause'] = 4
      }
      // 初始化红包
      if (message.type === 'red_envelope') {
        message['had_draw'] = 0
        message['isClick'] = 0
        this.setEnvelopeClickList(message, index)
      }
      if (index === 0 && (message.type === 'text' || message.type === 'text_quote')) {
        this.onSetText(message.payload.text)
      }
    },
    // 处理红包是否被点击
    setEnvelopeClickList(im, index) {
      if (envelopeClickList.includes(im.messageId)) {
        im['isClick'] = 1
      } else {
        im['isClick'] = 0
      }
    },
    // 发送信息后，将信息push到列表
    async pushList(message) {
      this.initMessageItem(message)
      // 监听到公告
      if (message.type === 'group_notice') {
        console.log('监听到公告')
        this.$refs.navigationRef.getData()
      }
      // 监听到修改群名
      if (message.type === 'update_group_name') {
        console.log('监听到修改群名')
        this.pagueObj.name = message.payload.name
      }

      // 将新消息添加到列表末尾（最新消息在底部）
      this.list.push(message)
      this.scrollToBottom()

      if (this.list.length < 20) {
        this.getHeight(true)
      }

      // 是否触发文字动效果
      if (message.type === 'text' || message.type === 'text_quote') {
        this.onSetText(message.payload.text)
      }
      // 是否触发红包雨
      if (message.type === 'red_envelope') {
        this.onSetRedEnvelope()
      }

      // 缓存照片地址，
      if (message.type === 'image' || message.type === 'image_transmit') {
        imageList.push(message.payload.url)
      }
    },

    // 文本触发效果相关========
    onSetText(text) {
      // 触发礼花
      throttle(() => {
        if (text.includes('[彩带]')) {
          this.$refs.mScreenAnimationLihua.show()
          uni.vibrateLong()
        }
      }, 4000)
    },
    // 触发红包雨
    onSetRedEnvelope() {
      throttle(() => {
        uni.vibrateLong()
      }, 4000)
    },
    bottomOperationScrollToBottom() {
      isBottomOperationScrollToBottom = true
      setTimeout(() => {
        isBottomOperationScrollToBottom = false
      }, 800)
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },
    // 页面滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        // 滚动到最大高度（底部）
        this.scroll_top = 999999
      })
    },
    // 点击某条信息
    onItem(item) {
      console.log(item)
      switch (item.type) {
        case 'video':
          this.playVideo(item)
          break
        case 'audio':
          this.playAudio(item)
          break
        case 'audio_quote':
          this.playAudio(item)
          break
        case 'image':
        case 'image_transmit':
          const index = imageList.indexOf(item.payload.url)
          if (index === -1) return openimg(imageList.length - 1, imageList)
          openimg(index, imageList)
          break
        case 'red_envelope':
          // 点击红包
          const fun = (code) => {
            this.renewItem(code, item)
          }
          uni.$off('open_red_packet')
          uni.$on('open_red_packet', fun)
          item['id'] = group.id
          break
        case 'map':
          getLocation({
            name: item.payload.title,
            address: item.payload.address,
            latitude: item.payload.latitude,
            longitude: item.payload.longitude
          })
          break
        case 'article':
          tofn(`/pagesOne/HTML/index?id=${item.payload.id}`)
          break
        case 'share_SBCF':
          tofn('/pagesSBCF/commodity_list/index', {
            id: item.payload.seller_id
          })
          break
        case 'share_mall':
          tofn(`/pagesShopping/details/index`, {
            goods_id: item.payload.goods_id
          })
          break
        case 'functional_module':
          tofn(item.payload.url)
          break
        default:
          break
      }
    },
    // 点击红包后更新那一条
    renewItem(code, item) {
      if (code === '0') {
        // 领取
        item.had_draw = 1
      } else {
        item.isClick = 1
      }
      // 不这样写某些情况下更新不了视图，
      for (let i = 0; i < this.list.length; i++) {
        if (this.list[i].messageId == item.messageId) {
          this.$set(this.list, i, {
            ...item
          })
          break
        }
      }
    },
    // 长按相关=======================
    // 长按某一条
    onLongpress(item, e) {
      this.$refs.operateRef.open(item, e)
    },
    // 引用
    quote(item) {
      this.$refs.bottomOperationRef.quote(item)
    },
    // 谢谢红包
    thank(item) {
      this.$refs.bottomOperationRef.thank(item)
    },
    // 转发
    transmit(item) {
      this.$refs.groupSelectionRef.open(item)
    },
    // 重新编辑
    recalledEdit(item) {
      this.$refs.bottomOperationRef.recalledEdit(item)
    },
    // @某人
    mention(item) {
      this.$refs.bottomOperationRef.mention(item)
    },
    // 视频相关========================
    // 点击了视频并播放
    playVideo(item) {
      this.videoPlayer.url = item.payload.video.url
      this.videoPlayer.show = true
      this.$nextTick(() => {
        this.videoPlayer.context.requestFullScreen({
          direction: 0
        })
        this.videoPlayer.context.play()
        this.videoPlayer.context.showStatusBar()
      })
    },
    // 退出全屏
    onVideoFullScreenChange(e) {
      //当退出全屏播放时，隐藏播放器
      if (this.videoPlayer.show && !e.detail.fullScreen) {
        this.videoPlayer.show = false
        this.videoPlayer.context.stop()
      }
    },
    // =============================================
    // 播放语音相关===========
    playAudio(item) {
      throttle(() => {
        // pause:1暂停;2播放完,3播放中,4初始状态
        if (item.messageId === audioItem?.messageId) {
          if (audioItem['pause'] == 3) {
            //正在播放
            // 暂停
            innerAudioContext.pause()
            innerAudioContext.offEnded()
            item['pause'] = 1
            audioItem['pause'] = 1
          } else if (audioItem['pause'] == 1 || audioItem['pause'] == 2) {
            //暂停或者播放中
            // 播放
            innerAudioContext.play()
          }
          return
        }

        audioItem['pause'] = '4'
        audioItem = item
        if (innerAudioContext) {
          try {
            innerAudioContext.pause()
            innerAudioContext.destroy()
            innerAudioContext = null
          } catch (e) {}
        }
        innerAudioContext = uni.createInnerAudioContext()
        innerAudioContext.src = item.payload.url
        innerAudioContext.play()
        innerAudioContext.offEnded()
        innerAudioContext.offPlay()
        innerAudioContext.onPlay(() => {
          // console.log('开始播放');
          item['pause'] = 3
          audioItem['pause'] = 3
        })
        innerAudioContext.onEnded(() => {
          // console.log('播放结束');
          item['pause'] = 2
          audioItem['pause'] = 2
        })
        innerAudioContext.onError((res) => {
          console.log('播放异常')
        })
      }, 500)
    },
    // ====================
    // 滚动中
    scroll(e) {
      scroll_top = e.detail.scrollTop
      this.$refs.operateRef.close()
      if (isBottomOperationScrollToBottom) return
      this.$refs.bottomOperationRef.closeAll()
    },
    // 滚动到底部
    scrolltolower() {
      if (this.history.allLoaded) return
      console.log('触底')
      this.loadHistoryMessage()
    },
    // 滚动到顶部
    scrolltoupper() {
      if (this.loading || this.loadend) return
      console.log('滚动到顶部，加载更多历史消息')
      // 获取第一条消息的ID作为分页参数
      const firstMessage = this.list[0]
      if (firstMessage) {
        this.loadData(firstMessage.id)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.page {
  position: fixed;
  z-index: 1;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: #ededed;
}

.scroll-Y {
  width: 100%;
  height: 0;
  transition: all 0.2s;
  background-color: #ededed;

  ::-webkit-scrollbar {
    display: none;
  }
}

.scroll-view-str {
  width: 100%;
}

.time {
  width: 100%;
  color: #a3a3a3;
  line-height: 100rpx;
}

.recalled {
  width: 100%;
  height: 50rpx;
  margin: 20rpx 0;
  color: #a3a3a3;

  .recalled-edit {
    color: #5a6693;
    margin-left: 14rpx;
  }
}
</style>
