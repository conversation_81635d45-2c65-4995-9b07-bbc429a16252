import { userInfo } from 'os'
import Vue from 'vue'
import Vuex from 'vuex'
Vue.use(Vuex)
const store = new Vuex.Store({
  state: {
    token:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImhiczExOSIsImNsaWVudGlkIjoiMTkyMTgyMjg4NzkwODU4MTM3NyIsImV4cCI6MTc1NTA1ODUyOX0.S24GcZb6K88dr6FdH82v9oqvTbBqVj9u9xOvd2b30_Y',
    userInfo: {
      userId: '1921822887908581377',
      nickname: '范发发',
      channelCode: 'hbs119',
      avatar: 'https://dummyimage.com/200x200/3c9cff/fff',
      token:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImhiczExOSIsImNsaWVudGlkIjoiMTkyMTgyMjg4NzkwODU4MTM3NyIsImV4cCI6MTc1NTA1ODUyOX0.S24'
    }
  },
  mutations: {
    // 设置导航栏高度
    SET_TOKEN(state, value) {
      state.token = value
    }
  }
})
export default store
