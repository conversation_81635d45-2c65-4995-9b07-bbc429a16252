{"version": 3, "sources": ["webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/m-share-sbcf.vue?381e", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/m-share-sbcf.vue?093d", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/m-share-sbcf.vue?eb0d", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/m-share-sbcf.vue?2eba", "uni-app:///pagesGoEasy/chat_page/components/item/m-share-sbcf.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/m-share-sbcf.vue?d12a", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/item/m-share-sbcf.vue?3043"], "names": ["props", "isMy", "type", "default", "value", "data", "computed", "methods", "onClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACiM;AACjM,gBAAgB,yLAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4KAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA+vB,CAAgB,krBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCiCnxB;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;EACA;EACAC;EACAC;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAA07C,CAAgB,yuCAAG,EAAC,C;;;;;;;;;;;ACA98C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/chat/components/item/m-share-sbcf.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./m-share-sbcf.vue?vue&type=template&id=a6362b9a&scoped=true&\"\nvar renderjs\nimport script from \"./m-share-sbcf.vue?vue&type=script&lang=js&\"\nexport * from \"./m-share-sbcf.vue?vue&type=script&lang=js&\"\nimport style0 from \"./m-share-sbcf.vue?vue&type=style&index=0&id=a6362b9a&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"a6362b9a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/chat/components/item/m-share-sbcf.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-share-sbcf.vue?vue&type=template&id=a6362b9a&scoped=true&\"", "var components\ntry {\n  components = {\n    mLine: function () {\n      return import(\n        /* webpackChunkName: \"components/m-line/m-line\" */ \"@/components/m-line/m-line.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-share-sbcf.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-share-sbcf.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"flex_c row\">\n\t\t<view class=\"flex_r text-box\" :class=\"{ text_box: isMy }\" @tap.stop=\"onClick\">\n\t\t\t<view class=\"text\" :class=\"isMy ? 'text_r' : 'text_l'\">\n\t\t\t\t<view class=\"flex_c_c article\">\n\t\t\t\t\t<view class=\"flex_r fa_c article-infr\">\n\t\t\t\t\t\t<view class=\"article-infr-img\">\n\t\t\t\t\t\t\t<image class=\"img\" :src=\"value.payload.share_image\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"text_22 color__ article-infr-text\">\n\t\t\t\t\t\t\t{{ value.payload.title }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"text_30 nowrap_ article-title\">\n\t\t\t\t\t\t{{ value.payload.short_title }}\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"article-img\">\n\t\t\t\t\t\t<image class=\"img\" :src=\"value.payload.share_image\" mode=\"aspectFill\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"m-line\">\n\t\t\t\t\t\t<m-line color=\"#f0f0f0\" length=\"100%\" :hairline=\"true\"></m-line>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"flex_r fa_c article-b\">\n\t\t\t\t\t\t<view class=\"article-b-icon\"></view>\n\t\t\t\t\t\t<view class=\"text_20 color__ article-b-text\">联盟商家</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tprops: {\n\t\tisMy: {\n\t\t\ttype: [Boolean, Number],\n\t\t\tdefault: false\n\t\t},\n\t\tvalue: {\n\t\t\ttype: Object,\n\t\t\tdefault: {}\n\t\t}\n\t},\n\tdata() {\n\t\treturn {};\n\t},\n\tcomputed: {},\n\tmethods: {\n\t\tonClick() {\n\t\t\tthis.$emit('onClick');\n\t\t}\n\t}\n};\n</script>\n\n<style scoped lang=\"scss\">\n.row {\n}\n.row_ {\n\tflex-direction: row-reverse;\n}\n.text_box {\n\tflex-direction: row-reverse;\n}\n.text {\n\tposition: relative;\n\tz-index: 99;\n\tbox-sizing: border-box;\n}\n\n.text_r {\n\tposition: relative;\n}\n.text_l {\n\tposition: relative;\n}\n\n.text_r::after {\n\tposition: absolute;\n\tz-index: -1;\n\tcontent: '';\n\ttop: 26rpx;\n\tright: -8rpx;\n\twidth: 18rpx;\n\theight: 18rpx;\n\tborder-radius: 2px;\n\ttransform: rotate(45deg);\n\tbackground-color: #fff;\n}\n.text_l::after {\n\tposition: absolute;\n\tz-index: -1;\n\tcontent: '';\n\ttop: 26rpx;\n\tleft: -8rpx;\n\twidth: 18rpx;\n\theight: 18rpx;\n\tborder-radius: 2px;\n\ttransform: rotate(45deg);\n\tbackground-color: #fff;\n}\n\n.article {\n\tbox-sizing: border-box;\n\tpadding: 14rpx 20rpx 4rpx 20rpx;\n\twidth: 490rpx;\n\tborder-radius: 10rpx;\n\toverflow: hidden;\n\tbackground-color: #fff;\n\tborder: 0.5px solid #fff;\n\n\t.article-infr {\n\t\twidth: 100%;\n\t\theight: 46rpx;\n\t\tmargin-bottom: 10rpx;\n\t\t.article-infr-img {\n\t\t\twidth: 40rpx;\n\t\t\theight: 40rpx;\n\t\t\tmargin-right: 10rpx;\n\t\t\tborder-radius: 50%;\n\t\t\toverflow: hidden;\n\t\t\tbackground-color: #f1f1f1;\n\t\t}\n\t\t.article-infr-text {\n\t\t}\n\t}\n\n\t.article-title {\n\t\tbox-sizing: border-box;\n\t\twidth: 100%;\n\t\tmargin-bottom: 14rpx;\n\t}\n\t.article-img {\n\t\twidth: 450rpx;\n\t\theight: 450rpx;\n\t\tbackground-color: #f1f1f1;\n\t}\n\t.m-line {\n\t\twidth: 100%;\n\t\theight: 1px;\n\t\tmargin-top: 20rpx;\n\t}\n\t.article-b {\n\t\twidth: 100%;\n\t\tmargin-top: 4rpx;\n\t\t.article-b-icon {\n\t\t\twidth: 26rpx;\n\t\t\theight: 26rpx;\n\t\t\tbackground-color: #f1f1f1;\n\t\t\tborder-radius: 50%;\n\t\t\toverflow: hidden;\n\t\t\tmargin-right: 10rpx;\n\t\t}\n\t\t.article-b-text {\n\t\t}\n\t}\n}\n</style>\n", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-share-sbcf.vue?vue&type=style&index=0&id=a6362b9a&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./m-share-sbcf.vue?vue&type=style&index=0&id=a6362b9a&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755051362406\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}