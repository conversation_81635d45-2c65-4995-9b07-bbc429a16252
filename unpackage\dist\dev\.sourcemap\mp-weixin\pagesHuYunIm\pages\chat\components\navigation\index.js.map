{"version": 3, "sources": ["webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/navigation/index.vue?d478", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/navigation/index.vue?12ff", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/navigation/index.vue?97c6", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/navigation/index.vue?e341", "uni-app:///pagesHuYunIm/pages/chat/components/navigation/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/navigation/index.vue?f59d", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/navigation/index.vue?92b8"], "names": ["props", "title", "type", "default", "groupCount", "group_id", "isPrivate", "data", "<PERSON><PERSON><PERSON><PERSON>", "showTopping", "pageData", "watch", "computed", "customBar", "statusBar", "userInfo", "renderTextMessage", "renderTextMessageNo", "created", "methods", "to", "onMore", "member_id", "open", "close", "maskClick", "getData", "res", "noticeReaded", "uni", "API_notice", "http", "API_noticeReaded", "notice_id"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACiM;AACjM,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAwvB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACqE5wB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AAEA;AACA;AAAA,eAGA;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;EACA;EACAI;IACA;MAEAC;MAEAC;MACAC;IACA;EACA;EACAC;IACAN;MACA;QACA;UACA;QACA;MACA;IACA;EACA;EACAO;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;EACA;EACAC;EACAC;IACAC;IACAC;MACA;QACA;UACAC;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACA;kBACApB;kBACA;kBACA;oBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAqB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACA5B;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA0B;gBACA;kBACA;kBACA;gBACA;gBACAE;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MACA;QACAC,UACA,gBACA;UACA1B;QACA,GACA,MACA;UACA;UACA;QACA,EACA;MACA;IACA;IACA;IACA2B;MAAA;MACA;QACAD,UACA,sBACA;UACA1B;UACA4B;QACA,GACA,MACA;UACA;UACA;QACA,EACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnNA;AAAA;AAAA;AAAA;AAAm7C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAv8C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/chat/components/navigation/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2861e4a0&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=2861e4a0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2861e4a0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/chat/components/navigation/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=2861e4a0&scoped=true&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view>\r\n    <view class=\"nowrap_ navigationBar-box\">\r\n      <view :style=\"{ height: statusBar - 8 + 'px' }\"></view>\r\n      <view class=\"flex_r fa_c fj_b nowrap_ navigationBar\" :style=\"{ height: customBar - statusBar - 8 + 'px' }\">\r\n        <view class=\"icon_ navigationBar-icon\" @click=\"to()\">\r\n          <image\r\n            class=\"img\"\r\n            src=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTMyNC4yMTIgNTExLjgwNkw3ODcuODkgNzMuMDgzYzE2LjE5NC0xNi42MyAxNi4xOTQtNDMuOTc1IDAtNjAuNjA1LTE2LjE5NS0xNi42My00Mi40OTYtMTYuNjMtNTguNjE0IDBMMjM1Ljc1IDQ3OS4zNmMtOC42NDcgOC45Ny0xMi4zNDUgMjAuOTM1LTExLjcxOSAzMi40NDYtLjY0NSAxMS45MDggMy4wNzIgMjMuODc0IDExLjcyIDMyLjgyNGw0OTMuNTA2IDQ2Ni44ODNjMTYuMTE4IDE2LjY1IDQyLjQzOCAxNi42NSA1OC42MTQgMCAxNi4xOTQtMTcuMDg1IDE2LjE5NC00My45NzUgMC02MC42MDVMMzI0LjIxIDUxMS44MDYiIGZpbGw9IiMxZDFkMWQiIGRhdGEtc3BtLWFuY2hvci1pZD0iYTMxM3guc2VhcmNoX2luZGV4LjAuaTguMmM2YTNhODFJY0NLRlYiIGNsYXNzPSJzZWxlY3RlZCIvPjwvc3ZnPg==\"\r\n            mode=\"aspectFill\"\r\n          ></image>\r\n        </view>\r\n        <view class=\"flex1 icon_ nowrap_ navigationBar-text bold_\">\r\n          <text class=\"nowrap_\">\r\n            {{ title }}\r\n            <text v-if=\"groupCount\">（{{ groupCount }}）</text>\r\n          </text>\r\n        </view>\r\n        <view class=\"navigationBar-icon\" @click=\"onMore\">\r\n          <image\r\n            class=\"img\"\r\n            src=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTcuMjI0OTk5OTk5OTk5OTk0IDUwNy4wMzNhMTE3LjIzIDExNy4yMyAwIDEgMCAyMzQuNDYgMCAxMTcuMjMgMTE3LjIzIDAgMSAwLTIzNC40NiAwek0zOTYuNjY3MDAwMDAwMDAwMDMgNTA3LjAzM2ExMTcuMjMgMTE3LjIzIDAgMSAwIDIzNC40NiAwIDExNy4yMyAxMTcuMjMgMCAxIDAtMjM0LjQ2IDB6TTc4NS45MjggNTA3LjAzM2ExMTcuMjMgMTE3LjIzIDAgMSAwIDIzNC40NiAwIDExNy4yMyAxMTcuMjMgMCAxIDAtMjM0LjQ2IDB6Ii8+PC9zdmc+\"\r\n            mode=\"aspectFill\"\r\n          ></image>\r\n        </view>\r\n        <!-- #ifdef MP -->\r\n        <view class=\"wx-srt\" :style=\"{ width: barWidth + 'px' }\"></view>\r\n        <!-- #endif -->\r\n      </view>\r\n\r\n      <!-- 置顶信息 -->\r\n      <view class=\"icon_ topping\" :class=\"{ topping_: showTopping }\" @click=\"open\">\r\n        <view class=\"topping-icon\">\r\n          <image\r\n            class=\"img\"\r\n            src=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTU5Ny42MjkgNzczLjMxNWgyMC4xNXYzLjQ3NmgtMjAuMTV2LTMuNDc2em04OC42NTQtMjEuNTA5aDQuOTg2djE2LjExOGgtNC45ODZ2LTE2LjExN3ptMTUuNzEzLS4xaDUuMDM4djE2LjIxOGgtNS4wMzh2LTE2LjIxOHptLTEwNC4zNjcgMjguMjA4aDIwLjE1djMuNDc1aC0yMC4xNXYtMy40NzV6bS00MzcuNTUtMzYwLjIyNmg3MDEuODM1djQ4Mi41M0gxNjAuMDh2LTQ4Mi41M3ptNzY3LjYzNyA1NTQuNzY1SDk0LjI4NFYzNTIuMjI2aDgzMy40MzJ2NjIyLjIyN3ptLTgwNy43NTUtMjUuNjg1aDc4Mi4wNDJWMzc3LjkwNEgxMTkuOTZ2NTcwLjg2NHptNjc1LjQ3LTYxMy43ODlMNTUwLjgwNyAxMzkuMjlsMTguOTg0LTIzLjcxNSAyNzQuNzcgMjE5LjQwNXptLTU2OC44NzggMGwyNDQuNjM1LTE5NS42OS0xOC45Ny0yMy43MTUtMjc0Ljc5MiAyMTkuNDA1ek01NTQuODQ2IDgzLjU1M2MwIDI0LjI0Ny0xOS42MzMgNDMuODc0LTQzLjg1IDQzLjg3NC0yNC4yMzEgMC00My44OC0xOS42MjctNDMuODgtNDMuODc0IDAtMjQuMjE1IDE5LjY0OS00My44NjUgNDMuODgtNDMuODY1IDI0LjIxNyAwIDQzLjg1IDE5LjY1IDQzLjg1IDQzLjg2NXoiIGZpbGw9IiNmZmMzMDEiLz48L3N2Zz4=\"\r\n            mode=\"aspectFill\"\r\n          ></image>\r\n        </view>\r\n        <view class=\"text_28 nowrap_ flex1 topping-text\">\r\n          <view class=\"nowrap_ text_32\" v-html=\"renderTextMessageNo\"></view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <uni-popup ref=\"popup\" type=\"top\" maskBackgroundColor=\"rgba(255, 255, 255, 0.7)\" @maskClick=\"maskClick\">\r\n      <view class=\"flex_c popup-box\">\r\n        <view class=\"popup-top\" :style=\"{ height: $store.state.StatusBar.customBar + 10 + 'px' }\"></view>\r\n        <view class=\"content\">\r\n          <view class=\"text_32\" :style=\"{ whiteSpace: 'pre-wrap' }\" v-html=\"renderTextMessage\"></view>\r\n        </view>\r\n        <view class=\"flex_r popup-box-bottom\">\r\n          <view class=\"icon_ text_30 bold_ size_white button\" @click=\"noticeReaded\">\r\n            <view class=\"button-icon\">\r\n              <image\r\n                class=\"img\"\r\n                src=\"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTgzMi44NTMgODY2Ljk4N2gtNjE0LjRjLTU4LjAyNiAwLTEwMi40LTQ0LjM3NC0xMDIuNC0xMDIuNFY0MzAuMDhjMC0zNy41NDcgMjAuNDgtNzEuNjggNTQuNjE0LTg4Ljc0N2wzMDcuMi0xNzAuNjY2YzMwLjcyLTE3LjA2NyA2NC44NTMtMTcuMDY3IDk1LjU3MyAwbDMyMC44NTMgMTYwLjQyNmMyMy44OTQgMTAuMjQgMzcuNTQ3IDM0LjEzNCAzNy41NDcgNTguMDI3cy0xMy42NTMgNDcuNzg3LTM0LjEzMyA2MS40NGwtMzE3LjQ0IDE4NC4zMmMtMzQuMTM0IDIwLjQ4LTc4LjUwNyAxNy4wNjctMTEyLjY0LTMuNDEzTDI2Ni4yNCA0ODguMTA3Yy0xMy42NTMtMTAuMjQtMjAuNDgtMzAuNzItNi44MjctNDcuNzg3czMwLjcyLTIwLjQ4IDQ3Ljc4Ny02LjgyN2wyMDEuMzg3IDE0My4zNmMxMC4yNCA2LjgyNyAyMy44OTMgNi44MjcgMzcuNTQ2IDMuNDE0bDMxNy40NC0xODQuMzJMNTQyLjcyIDIzNS41MmMtMTAuMjQtMy40MTMtMjAuNDgtMy40MTMtMzAuNzIgMEwyMDQuOCA0MDIuNzczYy0xMC4yNCA2LjgyNy0xNy4wNjcgMTcuMDY3LTE3LjA2NyAzMC43MlY3NjhjMCAyMC40OCAxMy42NTQgMzQuMTMzIDM0LjEzNCAzNC4xMzNoNjE0LjRjMjAuNDggMCAzNC4xMzMtMTMuNjUzIDM0LjEzMy0zNC4xMzNWNTYzLjJjMC0yMC40OCAxMy42NTMtMzQuMTMzIDM0LjEzMy0zNC4xMzNzMzQuMTM0IDEzLjY1MyAzNC4xMzQgMzQuMTMzVjc2OGMtMy40MTQgNTEuMi00Ny43ODcgOTguOTg3LTEwNS44MTQgOTguOTg3eiIgZmlsbD0iI2ZmZiIgZGF0YS1zcG0tYW5jaG9yLWlkPSJhMzEzeC5zZWFyY2hfaW5kZXguMC5pNS42NTgyM2E4MWJhWkhnMiIgY2xhc3M9InNlbGVjdGVkIi8+PC9zdmc+\"\r\n                mode=\"aspectFill\"\r\n              ></image>\r\n            </view>\r\n            已读\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex'\r\nimport { to, show } from '@/utils/index.js'\r\nimport { EmojiDecoder, emojiMap } from '../../../../lib/EmojiDecoder.js'\r\nconst emojiUrl = 'https://imgcache.qq.com/open/qcloud/tim/assets/emoji/'\r\nconst decoder = new EmojiDecoder(emojiUrl, emojiMap)\r\n// #ifdef MP\r\nconst menuButtonInfo = wx.getMenuButtonBoundingClientRect()\r\nlet barWidth = menuButtonInfo.width\r\n// #endif\r\n\r\nexport default {\r\n  props: {\r\n    title: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    groupCount: {\r\n      type: [String, Number],\r\n      default: ''\r\n    },\r\n    group_id: {\r\n      type: [String, Number],\r\n      default: ''\r\n    },\r\n    isPrivate: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // #ifdef MP\r\n      barWidth,\r\n      // #endif\r\n      showTopping: false,\r\n      pageData: {}\r\n    }\r\n  },\r\n  watch: {\r\n    group_id(value) {\r\n      if (value) {\r\n        if (!this.isPrivate) {\r\n          this.getData()\r\n        }\r\n      }\r\n    }\r\n  },\r\n  computed: mapState({\r\n    customBar: (state) => state.StatusBar.customBar,\r\n    statusBar: (state) => state.StatusBar.statusBar,\r\n    userInfo: (state) => state.userInfo,\r\n    //渲染文本消息，如果包含表情，替换为图片\r\n    //todo:本不需要该方法，可以在标签里完成，但小程序有兼容性问题，被迫这样实现\r\n    renderTextMessage() {\r\n      const text = this.pageData.content\r\n      if (!text) return ''\r\n      return '<span>' + decoder.decode(text) + '</span>'\r\n    },\r\n    renderTextMessageNo() {\r\n      const text = this.pageData.content\r\n      if (!text) return ''\r\n      return '<span>' + decoder.decodeNo(text) + '</span>'\r\n    }\r\n  }),\r\n  created() {},\r\n  methods: {\r\n    to,\r\n    onMore() {\r\n      if (this.isPrivate) {\r\n        to(`/pagesGoEasy/group_member_infor/index`, {\r\n          member_id: this.group_id\r\n        })\r\n      } else {\r\n        to(`/pagesGoEasy/group_infor/index?group_id=${this.group_id}`)\r\n      }\r\n    },\r\n    open() {\r\n      this.showTopping = false\r\n      this.$refs.popup.open()\r\n    },\r\n    close() {\r\n      this.showTopping = false\r\n      this.$refs.popup.close()\r\n    },\r\n    maskClick() {\r\n      this.showTopping = true\r\n    },\r\n    async getData() {\r\n      const res = await this.API_notice()\r\n      if (res) {\r\n        const data = res.data.data\r\n        this.pageData = data\r\n        if (!data.is_new) {\r\n          this.showTopping = true\r\n        }\r\n        if (!data.content) this.showTopping = false\r\n      }\r\n    },\r\n    async noticeReaded() {\r\n      uni.showLoading({\r\n        title: '加载中'\r\n      })\r\n      const res = await this.API_noticeReaded()\r\n      if (res) {\r\n        this.close()\r\n        show('标记已读', 2000, 'success')\r\n      }\r\n      uni.hideLoading()\r\n    },\r\n    API_notice() {\r\n      return new Promise((res) => {\r\n        http.post(\r\n          'Group/notice',\r\n          {\r\n            group_id: this.group_id\r\n          },\r\n          true,\r\n          (r) => {\r\n            if (r.data.code == 0) return res(r)\r\n            return show(r.data.msg), res(false)\r\n          }\r\n        )\r\n      })\r\n    },\r\n    // 已读\r\n    API_noticeReaded() {\r\n      return new Promise((res) => {\r\n        http.post(\r\n          'Group/noticeReaded',\r\n          {\r\n            group_id: this.group_id,\r\n            notice_id: this.pageData.notice_id\r\n          },\r\n          true,\r\n          (r) => {\r\n            if (r.data.code == 0) return res(r)\r\n            return show(r.data.msg), res(false)\r\n          }\r\n        )\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.topping {\r\n  position: relative;\r\n  top: -10rpx;\r\n  box-sizing: border-box;\r\n  padding: 0 20rpx;\r\n  width: calc(100% - 40rpx);\r\n  height: 0rpx;\r\n  margin: 0rpx auto;\r\n  box-sizing: border-box;\r\n  border-radius: 10rpx;\r\n  background-color: #fff;\r\n  overflow: hidden;\r\n  opacity: 0;\r\n  transition: all 0.3s;\r\n\r\n  .topping-icon {\r\n    width: 50rpx;\r\n    height: 50rpx;\r\n    margin-bottom: 4rpx;\r\n    margin-right: 16rpx;\r\n  }\r\n\r\n  .topping-text {\r\n  }\r\n}\r\n\r\n.topping_ {\r\n  position: relative;\r\n  top: 0rpx;\r\n  height: 80rpx;\r\n  margin: 20rpx auto;\r\n  opacity: 1;\r\n}\r\n\r\n.wx-srt {\r\n  height: 20rpx;\r\n}\r\n\r\n.popup-box {\r\n  box-sizing: border-box;\r\n  padding: 0 20rpx 20rpx 20rpx;\r\n  width: 100%;\r\n  max-height: 70vh;\r\n  border-radius: 0 0 20rpx 20rpx;\r\n  background-color: #fff;\r\n\r\n  .content {\r\n    box-sizing: border-box;\r\n    width: 100%;\r\n    overflow-x: auto;\r\n  }\r\n\r\n  .popup-box-bottom {\r\n    width: 100%;\r\n    height: 60rpx;\r\n    margin-top: 10rpx;\r\n    flex-direction: row-reverse;\r\n\r\n    .button {\r\n      width: 150rpx;\r\n      height: 60rpx;\r\n      border-radius: 10rpx;\r\n      background-color: #05c160;\r\n\r\n      .button-icon {\r\n        width: 44rpx;\r\n        height: 44rpx;\r\n        margin-bottom: 4rpx;\r\n        margin-right: 10rpx;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.navigationBar-box {\r\n  position: fixed;\r\n  z-index: 98;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  padding-bottom: 8px;\r\n  background-color: rgba(237, 237, 237, 0.9);\r\n  backdrop-filter: blur(10px);\r\n\r\n  .navigationBar {\r\n    width: 100%;\r\n\r\n    .navigationBar-icon {\r\n      width: 40rpx;\r\n      height: 40rpx;\r\n      margin: 0 30rpx;\r\n\r\n      .img {\r\n        width: 90%;\r\n        height: 90%;\r\n      }\r\n    }\r\n\r\n    .navigationBar-text {\r\n      font-size: 16px;\r\n      margin: 0 20rpx;\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2861e4a0&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2861e4a0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755051362377\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}