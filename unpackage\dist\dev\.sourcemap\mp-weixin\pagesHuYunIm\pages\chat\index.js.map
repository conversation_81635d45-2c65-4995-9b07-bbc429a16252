{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/index.vue?e680", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/index.vue?0bbb", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/index.vue?6f05", "uni-app:///pagesHuYunIm/pages/chat/index.vue", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/index.vue?e8b8", "webpack:///D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/index.vue?b9e6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "navigation", "bottomOperation", "item", "videoPlayerRef", "openRedPacket", "operate", "name", "data", "isHistoryGet", "reserveHeight", "keyboardheightchangeValue", "myid", "scroll_top", "userList", "groupCount", "pagueObj", "to", "history", "messages", "allLoaded", "videoPlayer", "show", "url", "context", "page", "pageSize", "groupId", "loading", "loadend", "list", "userMap", "mqttClient", "mqttPingInterval", "groupIdNew", "userArr", "groupInfo", "userInfo", "onLoad", "console", "imageList", "envelopeClickList", "lastMessageTimeStamp", "view", "boundingClientRect", "bottomOperationRefHeight", "exec", "onPageScroll", "onReady", "onUnload", "innerAudioContext", "computed", "page_font_size", "renderMessageDate", "isSelf", "userId", "envelope_top_opened", "methods", "loadHistoryMessage", "idEnd", "requestData", "id_end", "res", "err", "uni", "title", "icon", "initMqtt", "mqttUserInfo", "callbacks", "onConnect", "onMessage", "onReconnect", "onError", "onEnd", "processMessages", "avatar", "member_id", "group_id", "text", "connectMqtt", "mqttOptions", "protocolId", "protocolVersion", "clientId", "username", "password", "clean", "keepalive", "connectTimeout", "reconnectPeriod", "bindMqttEvents", "handleMqttMessage", "processChatMessage", "chatMsg", "convertToStandardFormat", "content", "createBy", "createTime", "id", "msgType", "payload", "senderData", "status", "sysOrgCode", "updateBy", "updateTime", "senderId", "messageId", "timestamp", "type", "recalled", "isHide", "markMessageAsRead", "showMessageNotification", "message", "nickname", "clearPingInterval", "clearInterval", "disconnectMqtt", "setHeight", "reserveHeightRef", "getHeight", "view2", "setTimeout", "imgLoad", "keyboardheightchange", "onPage", "touchmove", "onBottom", "focus", "onMessageReceived", "sendMessage", "markGroupMessageAsRead", "initMessageItem", "setEnvelopeClickList", "im", "pushList", "onSetText", "onSetRedEnvelope", "bottomOperationScrollToBottom", "isBottomOperationScrollToBottom", "scrollToBottom", "onItem", "address", "latitude", "longitude", "goods_id", "renewItem", "onLongpress", "quote", "thank", "transmit", "recalledEdit", "mention", "playVideo", "direction", "onVideoFullScreenChange", "playAudio", "audioItem", "scroll", "scrolltolower", "scrolltoupper"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2L;AAC3L,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAA0tB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACuG9uB;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AAEA;AACA;AACA;AACA;AAEA;;AAEA;AACA;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA,eACA;EACAC;IACA;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;QACAT;MACA;MACAU;MACA;MACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACAC;cACAH;cACA;cACA;cACA;cACA;cACAJ;cACA;cACA;cACAnB;cACA;cACA2B;cACAC;cACAC;cACA;cACAf;cACA;cACA;cACA;cACA;cACA;gBACA;gBACAgB,KACAC;kBACAC;gBACA,GACAC;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EAEAC;IACA;IACAjB;IACA;IACA;MACA;QACAkB;MACA;QACAX;MACA;IACA;EACA;EACAY;IACAC;MAAA;IAAA;IACA;IACAC;MAAA;MACA;QACA;QACA;UACA;QACA;QACA;QACA;;QAEA;QACA;UACA;QACA;;QAEA;QACA;QACA;UACA;UACA;UACA;YACA;UACA;QACA;QAEA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UAAAC;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;MACA;IACA;EACA;EAEAC;IACA;AACA;AACA;AACA;AACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAIA;;gBAEA;gBACAC;kBACAnC;kBACAC;kBACAC;gBAAA,GACAgC;kBAAAE;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA;gBAAAC;gBAAAC;gBAAA,KAEAA;kBAAA;kBAAA;gBAAA;gBACAxB;gBACAyB;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIA;kBACA;kBACA;;kBAEA;kBACAJ;oBACA;oBACA;oBACA;kBACA;;kBAEA;kBACA;oBACA;oBACA;kBACA;oBACA;oBACA;kBACA;kBACA;kBACA;kBACAvB;kBACA;kBACA;oBACA;sBACA;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAyB;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACA9B,2CACA;kBAAA;kBACA;kBAAA;kBACA;kBAAA;kBACA;kBAAA;kBACA;kBAAA;kBACA;kBAAA,CACA,EACA;kBACA+B,+CACA/B,iBACAA,2BACAA,sBACAA,gBACAA,yCACA,MACA;kBAEAgC;oBACAC;sBACA/B;sBACA;oBACA;oBACAgC;sBACA;oBACA;oBACAC;sBACAjC;sBACA;oBACA;oBACAkC;sBACAlC;sBACA;oBACA;oBACAmC;sBACAnC;sBACA;oBACA;kBACA,GAEA;kBACAP;gBACA;kBACAO;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;AACA;AACA;AACA;IACAoC;MAAA;MACAxD;QACA;UAAA;QAAA;QACA;QACA;UACAhB;YACAI;YACAqE;YACAC;YACAC;UACA;QACA;QACA;QACA;UACA;YACA3E;cAAA4E;YAAA;UACA;YACAxC;UACA;QACA;UACApC;YAAA4E;UAAA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBACAzC;gBAAA;cAAA;gBAIAF,+CAEA;gBACA4C;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA,GAEA;gBACA;;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAnD;gBACAyB;kBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAyB;MAAA;MACA;MAEA;;MAEA;MACA;QACApD;;QAEA;QACA;;QAEA;QACA;UACA;YACA;YACAA;UACA;QACA;;QAEA;QACA;UACA;YACAA;UACA;YACAA;UACA;QACA;MACA;;MAEA;MACA;QACAA;MACA;;MAEA;MACA;QACAA;MACA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAqD;MACA;QACA;QACArD;QACA;QACA;UACA;QACA;MACA;QACAA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAsD;MACA;MACA;QACA;QACAC;MACA;MAEA;QACA;QACA;;QAEA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;MAEA;QACAC;QACAC;QACAC;QACAvE;QACAwE;QACAC;QACAC;UAAAtB;QAAA;QACAuB;UACA1B;UACAE;UACAD;UACAtE;QACA;QACAgG;QACAC;QACAC;QACAC;QACAnD;QACA;QACAoD;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;QACA;QACA;UAAAtF;QAAA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAuF;MACA;QACA;UACA;YACAC;YACAC;UACA;QACA;MACA;QACA7E;MACA;IACA;IAEA;AACA;AACA;IACA8E;MACA;QACAC;QACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MAEA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACAC;IACA;IACAC;MAAA;MACA;QACA;QACA/E,KACAC;UACA;UACA;YACA;cACA;cACA+E,MACA/E;gBACA;gBACA;kBACAgF;oBACA;kBACA;gBACA;cACA,GACA9E;YACA;UACA;YACA;YACA;cACA8E;gBACA;cACA;YACA;UACA;QACA,GACA9E;MACA;IACA;IAEA;IACA+E;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IACAC;MACA;IAAA,CACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;QACAxF;QACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAyF;MACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;QACA;QACApE;MACA;IACA;IACA;IACAqE;MACA;IAAA,CACA;IACA;IACAC;MACAnB;MACA;MACA;QACAA;MACA;MACA;MACA;QACAA;QACAA;QACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAoB;MACA;QACAC;MACA;QACAA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACA;kBACAlG;kBACA;gBACA;gBACA;gBACA;kBACAA;kBACA;gBACA;;gBAEA;gBACA;gBACA;gBAEA;kBACA;gBACA;;gBAEA;gBACA;kBACA;gBACA;gBACA;gBACA;kBACA;gBACA;;gBAEA;gBACA;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAkG;MAAA;MACA;MACA;QACA;UACA;UACA1E;QACA;MACA;IACA;IACA;IACA2E;MACA;QACA3E;MACA;IACA;IACA4E;MAAA;MACAC;MACAjB;QACAiB;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACAxG;MACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;QACA;UACA;UACA;UACA;UACA;QACA;UACA;UACA;YACA;UACA;UACAyB;UACAA;UACA7D;UACA;QACA;UACA;YACAI;YACAyI;YACAC;YACAC;UACA;UACA;QACA;UACA;UACA;QACA;UACA;YACA/C;UACA;UACA;QACA;UACA;YACAgD;UACA;UACA;QACA;UACA;UACA;QACA;UACA;MAAA;IAEA;IACA;IACAC;MACA;QACA;QACAjJ;MACA;QACAA;MACA;MACA;MACA;QACA;UACA,0CACAA,MACA;UACA;QACA;MACA;IACA;IACA;IACA;IACAkJ;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;UACAC;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;QACA;MACA;IACA;IACA;IACA;IACAC;MACA;QAAA;QACA;QACA;UACA;YACA;YACA;YACA5G;YACAA;YACA/C;YACA4J;UACA;YACA;YACA;YACA7G;UACA;UACA;QACA;QAEA6G;QACAA;QACA;UACA;YACA7G;YACAA;YACAA;UACA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;UACA;UACA/C;UACA4J;QACA;QACA7G;UACA;UACA/C;UACA4J;QACA;QACA7G;UACAX;QACA;MACA;IACA;IACA;IACA;IACAyH;MACAnJ;MACA;MACA;MACA;IACA;IACA;IACAoJ;MACA;MACA1H;MACA;IACA;IACA;IACA2H;MACA;MACA3H;MACA;MACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3iCA;AAAA;AAAA;AAAA;AAA63C,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAj5C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pagesHuYunIm/pages/chat/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pagesHuYunIm/pages/chat/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=00dcdc92&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=00dcdc92&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"00dcdc92\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pagesHuYunIm/pages/chat/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=00dcdc92&scoped=true&\"", "var components\ntry {\n  components = {\n    mGroupSelection: function () {\n      return import(\n        /* webpackChunkName: \"components/m-group-selection/m-group-selection\" */ \"@/components/m-group-selection/m-group-selection.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = !item.isHide ? _vm.renderMessageDate(item, index) : null\n    var m1 = !item.isHide && !item.recalled ? _vm.isSelf(item.userId) : null\n    var m2 = !item.isHide && !!item.recalled ? _vm.isSelf(item.senderId) : null\n    var m3 =\n      !item.isHide && !!item.recalled\n        ? item.type === \"text\" && _vm.isSelf(item.senderId)\n        : null\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n      m2: m2,\n      m3: m3,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view :id=\"page_font_size\">\r\n    <view class=\"flex_c page\" @touchmove=\"touchmove\">\r\n      <view class=\"navigationRef\">\r\n        <navigation ref=\"navigationRef\" :groupCount=\"groupCount\" :title=\"pagueObj.name\" :group_id=\"pagueObj.id\"></navigation>\r\n      </view>\r\n      <scroll-view\r\n        class=\"flex1 scroll-Y\"\r\n        @tap.stop=\"onPage\"\r\n        id=\"scroll-view\"\r\n        lower-threshold=\"100\"\r\n        scroll-y\r\n        scroll-with-animation\r\n        :scroll-top=\"scroll_top\"\r\n        @scroll=\"scroll\"\r\n        @scrolltoupper=\"scrolltoupper\"\r\n        @scrolltolower=\"scrolltolower\"\r\n      >\r\n        <view class=\"scroll-view-str\" :style=\"{ height: `${reserveHeight}px` }\" v-if=\"reserveHeight > 0\"></view>\r\n        <view class=\"messageList_\">\r\n          <template v-for=\"(item, index) in list\">\r\n            <!-- #ifdef APP || H5 -->\r\n            <view class=\"z_index2\" :class=\"`oneheight_${index}`\" :key=\"item.id + index\" v-if=\"!item.isHide\">\r\n              <view class=\"icon_ text_26 color__ time\">\r\n                {{ renderMessageDate(item, index) }}\r\n              </view>\r\n              <view :key=\"item.messageId + index\" v-if=\"!item.recalled\">\r\n                <item\r\n                  :isMy=\"isSelf(item.senderId)\"\r\n                  :myid=\"myid\"\r\n                  :item=\"item\"\r\n                  @onClick=\"onItem\"\r\n                  @onLongpress=\"onLongpress\"\r\n                  @mention=\"mention\"\r\n                  @imgLoad=\"imgLoad\"\r\n                ></item>\r\n              </view>\r\n              <view class=\"icon_ text_26 recalled\" v-else>\r\n                <view class=\"\">\r\n                  <text v-if=\"isSelf(item.senderId)\">你</text>\r\n                  <text v-else>{{ item.senderData.name }}</text>\r\n                  撤回了一条消息\r\n                </view>\r\n                <view class=\"recalled-edit\" v-if=\"item.type === 'text' && isSelf(item.senderId)\" @click=\"recalledEdit(item)\">重新编辑</view>\r\n              </view>\r\n            </view>\r\n            <!-- #endif -->\r\n            <!-- #ifdef MP -->\r\n            <view class=\"z_index2\" :key=\"item.id\" v-if=\"!item.isHide\">\r\n              <view class=\"icon_ text_26 color__ time\">\r\n                {{ renderMessageDate(item, index) }}\r\n              </view>\r\n              <view :key=\"item.id\" v-if=\"!item.recalled\">\r\n                <item\r\n                  :isMy=\"isSelf(item.userId)\"\r\n                  :myid=\"myid\"\r\n                  :item=\"item\"\r\n                  @onClick=\"onItem\"\r\n                  @onLongpress=\"onLongpress\"\r\n                  @mention=\"mention\"\r\n                ></item>\r\n              </view>\r\n              <view class=\"icon_ text_26 recalled\" v-else>\r\n                <view class=\"\">\r\n                  <text v-if=\"isSelf(item.senderId)\">你</text>\r\n                  <text v-else>{{ item.senderData.name }}</text>\r\n                  撤回了一条消息\r\n                </view>\r\n                <view class=\"recalled-edit\" v-if=\"item.type === 'text' && isSelf(item.senderId)\" @click=\"recalledEdit(item)\">重新编辑</view>\r\n              </view>\r\n            </view>\r\n            <!-- #endif -->\r\n          </template>\r\n        </view>\r\n        <view :style=\"{ height: $store.state.StatusBar.customBar - 8 + 4 + 'px' }\"></view>\r\n      </scroll-view>\r\n      <view class=\"bottomOperationRef\">\r\n        <bottom-operation\r\n          ref=\"bottomOperationRef\"\r\n          :to=\"to\"\r\n          :userInfo=\"userInfo\"\r\n          :userList=\"userList\"\r\n          @pushList=\"pushList\"\r\n          @onBottom=\"onBottom\"\r\n          @backToBottom=\"bottomOperationScrollToBottom\"\r\n          @focus=\"focus\"\r\n          @keyboardheightchange=\"keyboardheightchange\"\r\n        ></bottom-operation>\r\n      </view>\r\n    </view>\r\n    <!-- 视频播放器 -->\r\n    <video-player-ref\r\n      v-model=\"videoPlayer.show\"\r\n      :url=\"videoPlayer.url\"\r\n      @onVideoFullScreenChange=\"onVideoFullScreenChange\"\r\n    ></video-player-ref>\r\n    <!-- 复制；撤回等操作 -->\r\n    <operate ref=\"operateRef\" @quote=\"quote\" @thank=\"thank\" @transmit=\"transmit\"></operate>\r\n    <!-- 转发选择聊天 -->\r\n    <m-group-selection ref=\"groupSelectionRef\" @sendMessage=\"sendMessage\"></m-group-selection>\r\n  </view>\r\n</template>\r\n<script>\r\nimport { 自己的信息, 对话数据 } from '@/TEST/index.js'\r\nimport navigation from './components/navigation/index.vue'\r\nimport bottomOperation from './components/bottom-operation/index.vue'\r\nimport item from './components/item/index'\r\nimport videoPlayerRef from './components/video-player/index'\r\nimport openRedPacket from './components/open-red-packet/index'\r\nimport operate from './components/operate/index'\r\nimport { mapState } from 'vuex'\r\nimport { msglist } from '../../api/public'\r\nimport mqttClient from '../../utils/mqttClient.js'\r\nimport mqtt from '../../lib/mqtt.min.js'\r\nimport store from '../../store/index.js'\r\nimport { SplitArray } from '../../utils'\r\nimport { show, formatDate, throttle, openimg, getLocation, to as tofn } from '@/utils/index.js'\r\nimport { createUserInfo } from '../../utils/mqttConfig.js'\r\n\r\nlet lastMessageTimeStamp = null\r\n\r\nlet envelopeClickList = []\r\nlet innerAudioContext = uni.createInnerAudioContext()\r\nlet audioItem = {}\r\nlet group = {}\r\n\r\nlet groupId = null\r\n\r\n// 浏览照片数组\r\nlet imageList = []\r\n\r\n// 是否是手动触发的列表滑动\r\nlet isBottomOperationScrollToBottom = false\r\n\r\nconst IMAGE_MAX_WIDTH = 200\r\nconst IMAGE_MAX_HEIGHT = 150\r\nlet scroll_top = 0\r\nlet reserveHeightRef = 0\r\nlet bottomOperationRefHeight = 0\r\nexport default {\r\n  components: {\r\n    // groupSelection,\r\n    navigation,\r\n    bottomOperation,\r\n    item,\r\n    videoPlayerRef,\r\n    openRedPacket,\r\n    operate\r\n  },\r\n  name: 'groupChat',\r\n  data() {\r\n    return {\r\n      isHistoryGet: false,\r\n      reserveHeight: 0,\r\n      keyboardheightchangeValue: 0,\r\n      myid: null,\r\n      scroll_top,\r\n      userList: [], //群成员列表\r\n      groupCount: '',\r\n      pagueObj: {\r\n        name: '饭搭子5人组'\r\n      },\r\n      to: {},\r\n      // 历史数据\r\n      history: {\r\n        messages: [],\r\n        allLoaded: false\r\n      },\r\n      videoPlayer: {\r\n        show: false,\r\n        url: '',\r\n        context: null\r\n      },\r\n      // 添加缺失的数据属性\r\n      page: 1,\r\n      pageSize: 50,\r\n      groupId: '',\r\n      loading: false,\r\n      loadend: false,\r\n      list: [],\r\n      userMap: {},\r\n      mqttClient: null,\r\n      mqttPingInterval: null,\r\n      groupIdNew: 0,\r\n      userArr: [], //群成员\r\n      groupInfo: {}, //群信息\r\n      userInfo: {} //用户信息\r\n    }\r\n  },\r\n  async onLoad(e) {\r\n    console.log('🚀 ~ onLoad ~ e:', e)\r\n    const groupInfo = JSON.parse(decodeURIComponent(e.groupInfo))\r\n    this.groupInfo = groupInfo\r\n    this.userInfo = JSON.parse(decodeURIComponent(e.userInfo))\r\n    this.userArr = groupInfo.userArr\r\n    // 设置MQTT库\r\n    mqttClient.setMqttLib(mqtt)\r\n    this.groupIdNew = groupInfo.id\r\n    //\r\n    scroll_top = 0\r\n    this.scroll_top = scroll_top\r\n    imageList = []\r\n    envelopeClickList = uni.getStorageSync('envelopeClickList') || []\r\n    lastMessageTimeStamp = e.lastMessageTimeStamp || null\r\n    this.isHistoryGet = e.lastMessageTimeStamp\r\n    groupId = groupInfo.id\r\n    //\r\n    this.myid = this.userInfo.userId\r\n    this.initMqtt()\r\n    this.loadHistoryMessage()\r\n    this.$nextTick(() => {\r\n      let view = uni.createSelectorQuery().select('.bottomOperationRef')\r\n      view\r\n        .boundingClientRect((ref) => {\r\n          bottomOperationRefHeight = ref.height\r\n        })\r\n        .exec()\r\n    })\r\n  },\r\n  onPageScroll() {\r\n    this.$refs.bottomOperationRef.closeAll()\r\n  },\r\n  onReady() {\r\n    this.videoPlayer.context = uni.createVideoContext('videoPlayer', this)\r\n  },\r\n\r\n  onUnload() {\r\n    // 页面卸载时清理资源\r\n    mqttClient.disconnect()\r\n    // 清理音频资源\r\n    if (innerAudioContext) {\r\n      try {\r\n        innerAudioContext.destroy()\r\n      } catch (error) {\r\n        console.warn('清理音频资源失败:', error)\r\n      }\r\n    }\r\n  },\r\n  computed: mapState({\r\n    page_font_size: (state) => state.page_font_size,\r\n    //显示时间\r\n    renderMessageDate() {\r\n      return (message, index) => {\r\n        // 检查 createTime 是否存在\r\n        if (!message.createTime) {\r\n          return ''\r\n        }\r\n        //正则替换 -\r\n        const createTimeStamp = new Date(message.createTime.replace(/-/g, '/')).getTime()\r\n\r\n        // 第一条消息总是显示时间\r\n        if (index === 0) {\r\n          return message.createTime\r\n        }\r\n\r\n        // 获取前一条消息\r\n        const prevMessage = this.list[index - 1]\r\n        if (prevMessage && prevMessage.createTime) {\r\n          const prevCreateTimeStamp = new Date(prevMessage.createTime.replace(/-/g, '/')).getTime()\r\n          // 如果当前消息比前一条消息晚3分钟以上，则显示时间\r\n          if (createTimeStamp - prevCreateTimeStamp > 3 * 60 * 1000) {\r\n            return message.createTime\r\n          }\r\n        }\r\n\r\n        return ''\r\n      }\r\n    },\r\n    // 是否本人isMy\r\n    isSelf() {\r\n      return (senderId) => {\r\n        const { userId = '' } = this.userInfo\r\n        return senderId === `${userId}`\r\n      }\r\n    },\r\n    envelope_top_opened() {\r\n      return (id) => {\r\n        return this.envelopeXollectionList.includes(id)\r\n      }\r\n    }\r\n  }),\r\n\r\n  methods: {\r\n    /**\r\n     * 加载消息数据\r\n     * @param {string} idEnd - 结束ID，用于分页加载\r\n     * @returns {Promise<void>}\r\n     */\r\n    async loadHistoryMessage(idEnd = '') {\r\n      // 防止重复加载和已加载完成的情况\r\n      if (this.loading || this.loadend) {\r\n        return\r\n      }\r\n      try {\r\n        this.loading = true\r\n\r\n        // 构建请求参数\r\n        const requestData = {\r\n          page: this.page,\r\n          pageSize: this.pageSize,\r\n          groupId: this.groupIdNew,\r\n          ...(idEnd && { id_end: idEnd })\r\n        }\r\n\r\n        const [res, err] = await msglist(requestData)\r\n\r\n        if (err) {\r\n          console.error('加载消息数据失败:', err)\r\n          uni.showToast({\r\n            title: '加载失败，请重试',\r\n            icon: 'none'\r\n          })\r\n          return\r\n        }\r\n\r\n        if (res && Array.isArray(res)) {\r\n          // 处理语音消息内容\r\n          this.processMessages(res)\r\n\r\n          // 确保消息按时间正序排列（最早的在前，最新的在后）\r\n          res.sort((a, b) => {\r\n            const timeA = new Date(a.createTime).getTime()\r\n            const timeB = new Date(b.createTime).getTime()\r\n            return timeA - timeB\r\n          })\r\n\r\n          // 合并数据到列表\r\n          if (idEnd) {\r\n            // 分页加载：将历史消息添加到列表开头\r\n            this.list = SplitArray(res, []).concat(this.list)\r\n          } else {\r\n            // 初始加载：直接设置列表\r\n            this.list = SplitArray(res, this.list)\r\n          }\r\n          // 检查是否已加载完所有数据\r\n          this.loadend = res.length < this.pageSize\r\n          console.log('消息数据加载成功:', res)\r\n          // 如果不是分页加载，滚动到底部\r\n          if (!idEnd) {\r\n            this.$nextTick(() => {\r\n              this.getHeight()\r\n            })\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('loadData 异常:', error)\r\n        uni.showToast({\r\n          title: '网络异常，请重试',\r\n          icon: 'none'\r\n        })\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    /**\r\n     * 初始化MQTT连接\r\n     */\r\n    async initMqtt() {\r\n      try {\r\n        // 从store获取用户信息，如果没有则使用默认值\r\n        const userInfo = createUserInfo(\r\n          '1921822887908581377', // userId\r\n          '范发发', // nickname\r\n          'hbs119', // channelCode\r\n          'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImhiczExOSIsImNsaWVudGlkIjoiMTkyMTgyMjg4NzkwODU4MTM3NyIsImV4cCI6MTc1NTA1ODUyOX0.S24GcZb6K88dr6FdH82v9oqvTbBqVj9u9xOvd2b30_Y', // token\r\n          'https://dummyimage.com/100x100/cccccc/ffffff?text=头像', // avatar\r\n          'DEV' // 环境\r\n        )\r\n        // 创建用户信息对象\r\n        const mqttUserInfo = createUserInfo(\r\n          userInfo.userId,\r\n          userInfo.nickname || '用户',\r\n          userInfo.channelCode,\r\n          userInfo.token,\r\n          userInfo.avatar || this.defaultAvatar,\r\n          'DEV'\r\n        )\r\n\r\n        const callbacks = {\r\n          onConnect: () => {\r\n            console.log('MQTT连接成功')\r\n            this.isConnected = true\r\n          },\r\n          onMessage: (topic, mqttMsg) => {\r\n            this.handleMqttMessage(mqttMsg)\r\n          },\r\n          onReconnect: () => {\r\n            console.log('MQTT重连中...')\r\n            this.isConnected = false\r\n          },\r\n          onError: (error) => {\r\n            console.error('MQTT连接错误:', error)\r\n            this.isConnected = false\r\n          },\r\n          onEnd: () => {\r\n            console.log('MQTT连接已断开')\r\n            this.isConnected = false\r\n          }\r\n        }\r\n\r\n        // 连接MQTT\r\n        mqttClient.connect(mqttUserInfo, callbacks)\r\n      } catch (error) {\r\n        console.error('初始化MQTT失败:', error)\r\n        this.isConnected = false\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 处理消息内容\r\n     * @param {Array} messages - 消息列表\r\n     */\r\n    processMessages(messages) {\r\n      messages.forEach((item) => {\r\n        const findUser = this.userArr.find((user) => user.userId === item.userId)\r\n        // 处理发送者数据\r\n        if (findUser) {\r\n          item.senderData = {\r\n            name: findUser.nickname,\r\n            avatar: findUser.avatar,\r\n            member_id: findUser.userId,\r\n            group_id: item.groupId\r\n          }\r\n        }\r\n        // 处理语音消息内容\r\n        if (item.msgType === 'voice' && typeof item.content === 'string' && item.content.length > 10) {\r\n          try {\r\n            item.payload = { text: JSON.parse(item.content) }\r\n          } catch (error) {\r\n            console.warn('解析语音消息内容失败:', error, item)\r\n          }\r\n        } else {\r\n          item.payload = { text: item.content }\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 连接MQTT服务器\r\n     * @returns {Promise<void>}\r\n     */\r\n    async connectMqtt() {\r\n      try {\r\n        // 检查必要的用户信息\r\n        if (!store.state?.userInfo?.userId || !store.state?.userInfo?.wsUrl) {\r\n          console.error('用户信息不完整，无法连接MQTT')\r\n          return\r\n        }\r\n\r\n        const userInfo = store.state.app.userInfo\r\n\r\n        // MQTT连接配置\r\n        const mqttOptions = {\r\n          protocolId: 'MQTT',\r\n          protocolVersion: 4,\r\n          clientId: userInfo.userId,\r\n          username: userInfo.channelCode,\r\n          password: store.state.app.token,\r\n          clean: false,\r\n          keepalive: 60,\r\n          connectTimeout: 30 * 1000,\r\n          reconnectPeriod: 1000\r\n        }\r\n\r\n        // 创建MQTT连接\r\n        this.mqttClient = mqtt.connect(userInfo.wsUrl, mqttOptions)\r\n\r\n        // 绑定事件监听器\r\n        this.bindMqttEvents()\r\n      } catch (error) {\r\n        console.error('MQTT连接失败:', error)\r\n        uni.showToast({\r\n          title: 'MQTT连接失败',\r\n          icon: 'none'\r\n        })\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 绑定MQTT事件监听器\r\n     */\r\n    bindMqttEvents() {\r\n      if (!this.mqttClient) return\r\n\r\n      const userInfo = store.state.app.userInfo\r\n\r\n      // 连接成功事件\r\n      this.mqttClient.on('connect', () => {\r\n        console.log('MQTT连接成功')\r\n\r\n        // 清除之前的心跳定时器\r\n        this.clearPingInterval()\r\n\r\n        // 设置心跳定时器\r\n        this.mqttPingInterval = setInterval(() => {\r\n          if (this.mqttClient && this.mqttClient.connected) {\r\n            this.mqttClient.publish(`/chat/server/${userInfo.userId}/ping`, '1')\r\n            console.log('MQTT心跳发送')\r\n          }\r\n        }, 10000)\r\n\r\n        // 订阅用户消息频道\r\n        this.mqttClient.subscribe(`/chat/client/${userInfo.userId}`, (err) => {\r\n          if (err) {\r\n            console.error('订阅消息频道失败:', err)\r\n          } else {\r\n            console.log('订阅消息频道成功')\r\n          }\r\n        })\r\n      })\r\n\r\n      // 重连事件\r\n      this.mqttClient.on('reconnect', () => {\r\n        console.log('MQTT重连中...')\r\n      })\r\n\r\n      // 错误事件\r\n      this.mqttClient.on('error', (error) => {\r\n        console.error('MQTT连接错误:', error)\r\n      })\r\n\r\n      // 连接结束事件\r\n      this.mqttClient.on('end', () => {\r\n        console.log('MQTT连接断开')\r\n        this.clearPingInterval()\r\n      })\r\n\r\n      // 消息接收事件\r\n      this.mqttClient.on('message', (topic, message) => {\r\n        this.handleMqttMessage(topic, message)\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 处理MQTT消息\r\n     * @param {string} _topic - 消息主题（暂未使用）\r\n     * @param {Buffer} message - 消息内容\r\n     */\r\n    handleMqttMessage(_topic, message) {\r\n      try {\r\n        const messageStr = message.toString()\r\n        console.log('收到MQTT消息:', messageStr)\r\n        const mqttMsg = JSON.parse(messageStr)\r\n        if (mqttMsg.command === 'chatMsg') {\r\n          this.processChatMessage(mqttMsg.data)\r\n        }\r\n      } catch (error) {\r\n        console.error('处理MQTT消息失败:', error)\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 处理聊天消息\r\n     * @param {Object} chatMsg - 聊天消息数据\r\n     */\r\n    processChatMessage(chatMsg) {\r\n      // 如果消息格式不完整，转换为标准格式\r\n      if (!chatMsg.id || !chatMsg.senderData) {\r\n        const standardMessage = this.convertToStandardFormat(chatMsg)\r\n        chatMsg = standardMessage\r\n      }\r\n\r\n      if (this.groupIdNew === chatMsg.groupId) {\r\n        // 当前群组消息，添加到列表\r\n        this.pushList(chatMsg)\r\n\r\n        // 标记消息为已读\r\n        this.markMessageAsRead(chatMsg.groupId)\r\n      } else {\r\n        // 其他群组消息，显示通知\r\n        this.showMessageNotification(chatMsg)\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 将旧格式消息转换为标准格式\r\n     * @param {Object} oldMsg - 旧格式消息\r\n     * @returns {Object} 标准格式消息\r\n     */\r\n    convertToStandardFormat(oldMsg) {\r\n      const now = new Date()\r\n      const createTime = oldMsg.createTime || now.toISOString().slice(0, 19).replace('T', ' ')\r\n      const messageId = oldMsg.id || oldMsg.messageId || `${oldMsg.groupId}_${Date.now().toString().slice(-6)}`\r\n\r\n      return {\r\n        content: oldMsg.content || oldMsg.payload?.text || '',\r\n        createBy: null,\r\n        createTime: createTime,\r\n        groupId: oldMsg.groupId,\r\n        id: messageId,\r\n        msgType: oldMsg.msgType || oldMsg.type || 'text',\r\n        payload: oldMsg.payload || { text: oldMsg.content },\r\n        senderData: oldMsg.senderData || {\r\n          avatar: this.userMap[oldMsg.userId]?.avatar || '',\r\n          group_id: oldMsg.groupId,\r\n          member_id: oldMsg.userId,\r\n          name: this.userMap[oldMsg.userId]?.nickname || oldMsg.nickname || ''\r\n        },\r\n        status: \"正常\",\r\n        sysOrgCode: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        userId: oldMsg.userId,\r\n        // 保留原有字段以兼容现有逻辑\r\n        senderId: oldMsg.userId,\r\n        messageId: messageId,\r\n        timestamp: oldMsg.timestamp || Date.now(),\r\n        type: oldMsg.msgType || oldMsg.type || 'text',\r\n        recalled: oldMsg.recalled || false,\r\n        isHide: 0\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 标记消息为已读\r\n     * @param {string} groupId - 群组ID\r\n     */\r\n    markMessageAsRead(groupId) {\r\n      if (this.mqttClient && this.mqttClient.connected) {\r\n        const userInfo = store.state.app.userInfo\r\n        const readMessage = JSON.stringify({ groupId })\r\n        this.mqttClient.publish(`/chat/server/${userInfo.userId}/read`, readMessage)\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 显示消息通知\r\n     * @param {Object} chatMsg - 聊天消息\r\n     */\r\n    showMessageNotification(chatMsg) {\r\n      try {\r\n        if (this.$refs.notice) {\r\n          this.$refs.notice.add({\r\n            message: chatMsg.content,\r\n            nickname: chatMsg.nickname\r\n          })\r\n        }\r\n      } catch (error) {\r\n        console.error('显示消息通知失败:', error)\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 清除心跳定时器\r\n     */\r\n    clearPingInterval() {\r\n      if (this.mqttPingInterval) {\r\n        clearInterval(this.mqttPingInterval)\r\n        this.mqttPingInterval = null\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 断开MQTT连接\r\n     */\r\n    disconnectMqtt() {\r\n      this.clearPingInterval()\r\n\r\n      if (this.mqttClient) {\r\n        this.mqttClient.end()\r\n        this.mqttClient = null\r\n      }\r\n    },\r\n    setHeight(e) {\r\n      // 只计算导航栏的高度\r\n      const customBar = this.$store.state.StatusBar.customBar\r\n      this.reserveHeight = customBar\r\n      reserveHeightRef = customBar\r\n    },\r\n    getHeight(e) {\r\n      this.$nextTick(() => {\r\n        let view = uni.createSelectorQuery().select('.messageList_')\r\n        view\r\n          .boundingClientRect((select) => {\r\n            if (!select) return\r\n            if (!select?.height) {\r\n              this.$nextTick(() => {\r\n                let view2 = uni.createSelectorQuery().select('.messageList_')\r\n                view2\r\n                  .boundingClientRect((select) => {\r\n                    this.setHeight(select.height)\r\n                    if (e) {\r\n                      setTimeout(() => {\r\n                        this.reserveHeight = this.reserveHeight - this.keyboardheightchangeValue\r\n                      })\r\n                    }\r\n                  })\r\n                  .exec()\r\n              })\r\n            } else {\r\n              this.setHeight(select.height)\r\n              if (e) {\r\n                setTimeout(() => {\r\n                  this.reserveHeight = this.reserveHeight - this.keyboardheightchangeValue\r\n                })\r\n              }\r\n            }\r\n          })\r\n          .exec()\r\n      })\r\n    },\r\n\r\n    //图片加载完成\r\n    imgLoad() {\r\n      if (this.list.length > 20) return\r\n      this.getHeight(true)\r\n    },\r\n    keyboardheightchange(e, e2 = false) {\r\n      this.keyboardheightchangeValue = e\r\n      if (reserveHeightRef) {\r\n        this.reserveHeight = reserveHeightRef - e\r\n      }\r\n      if (e === 0) {\r\n        if (e2) return\r\n        this.getHeight()\r\n      }\r\n    },\r\n\r\n    // 点击整个页面\r\n    onPage() {\r\n      this.$refs.bottomOperationRef.close()\r\n      this.$refs.operateRef.close()\r\n    },\r\n    touchmove() {\r\n      // this.$refs.bottomOperationRef.closeAll();\r\n    },\r\n    onBottom() {\r\n      this.$refs.operateRef.close()\r\n    },\r\n    // 输入框获取焦点\r\n    focus() {\r\n      if (this.isHistoryGet) {\r\n        this.isHistoryGet = false\r\n        lastMessageTimeStamp = null\r\n        this.list = []\r\n        this.loadHistoryMessage()\r\n      }\r\n    },\r\n    // 获取聊天记录\r\n    // loadHistoryMessage() {\r\n    //   uni.hideLoading()\r\n    //   let list = JSON.parse(JSON.stringify(对话数据))\r\n    //   list = list.reverse()\r\n    //   // 同步混入数据\r\n    //   list.forEach((im) => {\r\n    //     // 缓存照片地址，\r\n    //     if (im.type === 'image' || im.type === 'image_transmit') {\r\n    //       imageList.unshift(im.payload.url)\r\n    //     }\r\n    //   })\r\n    //   // 模拟只有少量数据\r\n    //   // this.history.messages = [list[0],list[1],list[2]];\r\n    //   this.history.messages = [...this.history.messages, ...list]\r\n    //   if (this.history.messages.length > 20) return\r\n    //   this.$nextTick(() => {\r\n    //     this.getHeight()\r\n    //   })\r\n    // },\r\n    onMessageReceived(message) {\r\n      if (message.groupId === group.id) {\r\n        // push进列表\r\n        this.pushList(message)\r\n        //聊天时，收到消息标记为已读\r\n        this.markGroupMessageAsRead()\r\n      }\r\n    },\r\n    // 转发成功后\r\n    sendMessage(message) {\r\n      // push进列表\r\n      if (message.groupId === groupId) {\r\n        this.pushList(message)\r\n        // 同步消息到首页\r\n        uni.$emit('onMessageReceived', message)\r\n      }\r\n    },\r\n    // 将信息设置为已读\r\n    markGroupMessageAsRead() {\r\n      //\r\n    },\r\n    // 组装item\r\n    initMessageItem(message, index) {\r\n      message['isHide'] = 0\r\n      // 初始化语音\r\n      if (message.type === 'audio') {\r\n        message['pause'] = 4\r\n      }\r\n      // 初始化红包\r\n      if (message.type === 'red_envelope') {\r\n        message['had_draw'] = 0\r\n        message['isClick'] = 0\r\n        this.setEnvelopeClickList(message, index)\r\n      }\r\n      if (index === 0 && (message.type === 'text' || message.type === 'text_quote')) {\r\n        this.onSetText(message.payload.text)\r\n      }\r\n    },\r\n    // 处理红包是否被点击\r\n    setEnvelopeClickList(im, index) {\r\n      if (envelopeClickList.includes(im.messageId)) {\r\n        im['isClick'] = 1\r\n      } else {\r\n        im['isClick'] = 0\r\n      }\r\n    },\r\n    // 发送信息后，将信息push到列表\r\n    async pushList(message) {\r\n      this.initMessageItem(message)\r\n      // 监听到公告\r\n      if (message.type === 'group_notice') {\r\n        console.log('监听到公告')\r\n        this.$refs.navigationRef.getData()\r\n      }\r\n      // 监听到修改群名\r\n      if (message.type === 'update_group_name') {\r\n        console.log('监听到修改群名')\r\n        this.pagueObj.name = message.payload.name\r\n      }\r\n\r\n      // 将新消息添加到列表末尾（最新消息在底部）\r\n      this.list.push(message)\r\n      this.scrollToBottom()\r\n\r\n      if (this.list.length < 20) {\r\n        this.getHeight(true)\r\n      }\r\n\r\n      // 是否触发文字动效果\r\n      if (message.type === 'text' || message.type === 'text_quote') {\r\n        this.onSetText(message.payload.text)\r\n      }\r\n      // 是否触发红包雨\r\n      if (message.type === 'red_envelope') {\r\n        this.onSetRedEnvelope()\r\n      }\r\n\r\n      // 缓存照片地址，\r\n      if (message.type === 'image' || message.type === 'image_transmit') {\r\n        imageList.push(message.payload.url)\r\n      }\r\n    },\r\n\r\n    // 文本触发效果相关========\r\n    onSetText(text) {\r\n      // 触发礼花\r\n      throttle(() => {\r\n        if (text.includes('[彩带]')) {\r\n          this.$refs.mScreenAnimationLihua.show()\r\n          uni.vibrateLong()\r\n        }\r\n      }, 4000)\r\n    },\r\n    // 触发红包雨\r\n    onSetRedEnvelope() {\r\n      throttle(() => {\r\n        uni.vibrateLong()\r\n      }, 4000)\r\n    },\r\n    bottomOperationScrollToBottom() {\r\n      isBottomOperationScrollToBottom = true\r\n      setTimeout(() => {\r\n        isBottomOperationScrollToBottom = false\r\n      }, 800)\r\n      this.$nextTick(() => {\r\n        this.scrollToBottom()\r\n      })\r\n    },\r\n    // 页面滚动到底部\r\n    scrollToBottom() {\r\n      this.$nextTick(() => {\r\n        // 滚动到最大高度（底部）\r\n        this.scroll_top = 999999\r\n      })\r\n    },\r\n    // 点击某条信息\r\n    onItem(item) {\r\n      console.log(item)\r\n      switch (item.type) {\r\n        case 'video':\r\n          this.playVideo(item)\r\n          break\r\n        case 'audio':\r\n          this.playAudio(item)\r\n          break\r\n        case 'audio_quote':\r\n          this.playAudio(item)\r\n          break\r\n        case 'image':\r\n        case 'image_transmit':\r\n          const index = imageList.indexOf(item.payload.url)\r\n          if (index === -1) return openimg(imageList.length - 1, imageList)\r\n          openimg(index, imageList)\r\n          break\r\n        case 'red_envelope':\r\n          // 点击红包\r\n          const fun = (code) => {\r\n            this.renewItem(code, item)\r\n          }\r\n          uni.$off('open_red_packet')\r\n          uni.$on('open_red_packet', fun)\r\n          item['id'] = group.id\r\n          break\r\n        case 'map':\r\n          getLocation({\r\n            name: item.payload.title,\r\n            address: item.payload.address,\r\n            latitude: item.payload.latitude,\r\n            longitude: item.payload.longitude\r\n          })\r\n          break\r\n        case 'article':\r\n          tofn(`/pagesOne/HTML/index?id=${item.payload.id}`)\r\n          break\r\n        case 'share_SBCF':\r\n          tofn('/pagesSBCF/commodity_list/index', {\r\n            id: item.payload.seller_id\r\n          })\r\n          break\r\n        case 'share_mall':\r\n          tofn(`/pagesShopping/details/index`, {\r\n            goods_id: item.payload.goods_id\r\n          })\r\n          break\r\n        case 'functional_module':\r\n          tofn(item.payload.url)\r\n          break\r\n        default:\r\n          break\r\n      }\r\n    },\r\n    // 点击红包后更新那一条\r\n    renewItem(code, item) {\r\n      if (code === '0') {\r\n        // 领取\r\n        item.had_draw = 1\r\n      } else {\r\n        item.isClick = 1\r\n      }\r\n      // 不这样写某些情况下更新不了视图，\r\n      for (let i = 0; i < this.list.length; i++) {\r\n        if (this.list[i].messageId == item.messageId) {\r\n          this.$set(this.list, i, {\r\n            ...item\r\n          })\r\n          break\r\n        }\r\n      }\r\n    },\r\n    // 长按相关=======================\r\n    // 长按某一条\r\n    onLongpress(item, e) {\r\n      this.$refs.operateRef.open(item, e)\r\n    },\r\n    // 引用\r\n    quote(item) {\r\n      this.$refs.bottomOperationRef.quote(item)\r\n    },\r\n    // 谢谢红包\r\n    thank(item) {\r\n      this.$refs.bottomOperationRef.thank(item)\r\n    },\r\n    // 转发\r\n    transmit(item) {\r\n      this.$refs.groupSelectionRef.open(item)\r\n    },\r\n    // 重新编辑\r\n    recalledEdit(item) {\r\n      this.$refs.bottomOperationRef.recalledEdit(item)\r\n    },\r\n    // @某人\r\n    mention(item) {\r\n      this.$refs.bottomOperationRef.mention(item)\r\n    },\r\n    // 视频相关========================\r\n    // 点击了视频并播放\r\n    playVideo(item) {\r\n      this.videoPlayer.url = item.payload.video.url\r\n      this.videoPlayer.show = true\r\n      this.$nextTick(() => {\r\n        this.videoPlayer.context.requestFullScreen({\r\n          direction: 0\r\n        })\r\n        this.videoPlayer.context.play()\r\n        this.videoPlayer.context.showStatusBar()\r\n      })\r\n    },\r\n    // 退出全屏\r\n    onVideoFullScreenChange(e) {\r\n      //当退出全屏播放时，隐藏播放器\r\n      if (this.videoPlayer.show && !e.detail.fullScreen) {\r\n        this.videoPlayer.show = false\r\n        this.videoPlayer.context.stop()\r\n      }\r\n    },\r\n    // =============================================\r\n    // 播放语音相关===========\r\n    playAudio(item) {\r\n      throttle(() => {\r\n        // pause:1暂停;2播放完,3播放中,4初始状态\r\n        if (item.messageId === audioItem?.messageId) {\r\n          if (audioItem['pause'] == 3) {\r\n            //正在播放\r\n            // 暂停\r\n            innerAudioContext.pause()\r\n            innerAudioContext.offEnded()\r\n            item['pause'] = 1\r\n            audioItem['pause'] = 1\r\n          } else if (audioItem['pause'] == 1 || audioItem['pause'] == 2) {\r\n            //暂停或者播放中\r\n            // 播放\r\n            innerAudioContext.play()\r\n          }\r\n          return\r\n        }\r\n\r\n        audioItem['pause'] = '4'\r\n        audioItem = item\r\n        if (innerAudioContext) {\r\n          try {\r\n            innerAudioContext.pause()\r\n            innerAudioContext.destroy()\r\n            innerAudioContext = null\r\n          } catch (e) {}\r\n        }\r\n        innerAudioContext = uni.createInnerAudioContext()\r\n        innerAudioContext.src = item.payload.url\r\n        innerAudioContext.play()\r\n        innerAudioContext.offEnded()\r\n        innerAudioContext.offPlay()\r\n        innerAudioContext.onPlay(() => {\r\n          // console.log('开始播放');\r\n          item['pause'] = 3\r\n          audioItem['pause'] = 3\r\n        })\r\n        innerAudioContext.onEnded(() => {\r\n          // console.log('播放结束');\r\n          item['pause'] = 2\r\n          audioItem['pause'] = 2\r\n        })\r\n        innerAudioContext.onError((res) => {\r\n          console.log('播放异常')\r\n        })\r\n      }, 500)\r\n    },\r\n    // ====================\r\n    // 滚动中\r\n    scroll(e) {\r\n      scroll_top = e.detail.scrollTop\r\n      this.$refs.operateRef.close()\r\n      if (isBottomOperationScrollToBottom) return\r\n      this.$refs.bottomOperationRef.closeAll()\r\n    },\r\n    // 滚动到底部\r\n    scrolltolower() {\r\n      if (this.history.allLoaded) return\r\n      console.log('触底')\r\n      this.loadHistoryMessage()\r\n    },\r\n    // 滚动到顶部\r\n    scrolltoupper() {\r\n      if (this.loading || this.loadend) return\r\n      console.log('滚动到顶部，加载更多历史消息')\r\n      // 获取第一条消息的ID作为分页参数\r\n      const firstMessage = this.list[0]\r\n      if (firstMessage) {\r\n        this.loadData(firstMessage.id)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.page {\r\n  position: fixed;\r\n  z-index: 1;\r\n  top: 0;\r\n  left: 0;\r\n  bottom: 0;\r\n  right: 0;\r\n  background-color: #ededed;\r\n}\r\n\r\n.scroll-Y {\r\n  width: 100%;\r\n  height: 0;\r\n  transition: all 0.2s;\r\n  background-color: #ededed;\r\n\r\n  ::-webkit-scrollbar {\r\n    display: none;\r\n  }\r\n}\r\n\r\n.scroll-view-str {\r\n  width: 100%;\r\n}\r\n\r\n.time {\r\n  width: 100%;\r\n  color: #a3a3a3;\r\n  line-height: 100rpx;\r\n}\r\n\r\n.recalled {\r\n  width: 100%;\r\n  height: 50rpx;\r\n  margin: 20rpx 0;\r\n  color: #a3a3a3;\r\n\r\n  .recalled-edit {\r\n    color: #5a6693;\r\n    margin-left: 14rpx;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=00dcdc92&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=00dcdc92&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755053555576\n      var cssReload = require(\"D:/Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}