(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pagesHuYunIm/pages/chat/components/bottom-operation/index"],{

/***/ 242:
/*!**************************************************************************************************************!*\
  !*** D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/bottom-operation/index.vue ***!
  \**************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_0c5cade1_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=0c5cade1&scoped=true& */ 243);
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ 245);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _index_vue_vue_type_style_index_0_id_0c5cade1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&id=0c5cade1&lang=scss&scoped=true& */ 247);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 43);

var renderjs





/* normalize component */

var component = Object(_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_3__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_0c5cade1_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _index_vue_vue_type_template_id_0c5cade1_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "0c5cade1",
  null,
  false,
  _index_vue_vue_type_template_id_0c5cade1_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pagesHuYunIm/pages/chat/components/bottom-operation/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 243:
/*!*********************************************************************************************************************************************************!*\
  !*** D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/bottom-operation/index.vue?vue&type=template&id=0c5cade1&scoped=true& ***!
  \*********************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_0c5cade1_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=0c5cade1&scoped=true& */ 244);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_0c5cade1_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_0c5cade1_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_0c5cade1_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_0c5cade1_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 244:
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/bottom-operation/index.vue?vue&type=template&id=0c5cade1&scoped=true& ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    mBottomPaceholder: function () {
      return __webpack_require__.e(/*! import() | components/m-bottom-paceholder/m-bottom-paceholder */ "components/m-bottom-paceholder/m-bottom-paceholder").then(__webpack_require__.bind(null, /*! @/components/m-bottom-paceholder/m-bottom-paceholder.vue */ 356))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  var g0 = _vm.text.length
  _vm.$mp.data = Object.assign(
    {},
    {
      $root: {
        g0: g0,
      },
    }
  )
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 245:
/*!***************************************************************************************************************************************!*\
  !*** D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/bottom-operation/index.vue?vue&type=script&lang=js& ***!
  \***************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js& */ 246);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Development_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 246:
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/bottom-operation/index.vue?vue&type=script&lang=js& ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 59));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 61));
var _defineProperty2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/defineProperty */ 11));
var _index = __webpack_require__(/*! @/utils/index.js */ 30);
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { (0, _defineProperty2.default)(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
var emoji = function emoji() {
  Promise.all(/*! require.ensure | pagesHuYunIm/pages/chat/components/bottom-operation/emoji */[__webpack_require__.e("common/vendor"), __webpack_require__.e("pagesHuYunIm/pages/chat/components/bottom-operation/emoji")]).then((function () {
    return resolve(__webpack_require__(/*! ./emoji.vue */ 393));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var more = function more() {
  __webpack_require__.e(/*! require.ensure | pagesHuYunIm/pages/chat/components/bottom-operation/more */ "pagesHuYunIm/pages/chat/components/bottom-operation/more").then((function () {
    return resolve(__webpack_require__(/*! ./more.vue */ 400));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var mRecorder = function mRecorder() {
  Promise.all(/*! require.ensure | pagesHuYunIm/pages/chat/components/bottom-operation/m-recorder */[__webpack_require__.e("common/vendor"), __webpack_require__.e("pagesHuYunIm/pages/chat/components/bottom-operation/m-recorder")]).then((function () {
    return resolve(__webpack_require__(/*! ./m-recorder.vue */ 407));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var memberSelectionLoading = function memberSelectionLoading() {
  __webpack_require__.e(/*! require.ensure | pagesHuYunIm/components/memberSelection/index */ "pagesHuYunIm/components/memberSelection/index").then((function () {
    return resolve(__webpack_require__(/*! ../../../../components/memberSelection/index.vue */ 415));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var mText = function mText() {
  Promise.all(/*! require.ensure | pagesHuYunIm/pages/chat/components/item/quoteType/m-text */[__webpack_require__.e("common/vendor"), __webpack_require__.e("pagesHuYunIm/pages/chat/components/item/quoteType/m-text")]).then((function () {
    return resolve(__webpack_require__(/*! ../item/quoteType/m-text.vue */ 424));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var mImage = function mImage() {
  __webpack_require__.e(/*! require.ensure | pagesHuYunIm/pages/chat/components/item/quoteType/m-image */ "pagesHuYunIm/pages/chat/components/item/quoteType/m-image").then((function () {
    return resolve(__webpack_require__(/*! ../item/quoteType/m-image.vue */ 431));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var mAudio = function mAudio() {
  __webpack_require__.e(/*! require.ensure | pagesHuYunIm/pages/chat/components/item/quoteType/m-audio */ "pagesHuYunIm/pages/chat/components/item/quoteType/m-audio").then((function () {
    return resolve(__webpack_require__(/*! ../item/quoteType/m-audio.vue */ 438));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var mOther = function mOther() {
  Promise.all(/*! require.ensure | pagesHuYunIm/pages/chat/components/item/quoteType/m-other */[__webpack_require__.e("common/vendor"), __webpack_require__.e("pagesHuYunIm/pages/chat/components/item/quoteType/m-other")]).then((function () {
    return resolve(__webpack_require__(/*! ../item/quoteType/m-other.vue */ 445));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var recorderManager = uni.getRecorderManager();
//录音时长
var startTime = 0;
//
var inputValue = '';
var getSelectedTextRangeSetInterval = null;
var cursor = 0; //输入框光标
var _default2 = {
  components: {
    emoji: emoji,
    more: more,
    mRecorder: mRecorder,
    // memberSelection,
    memberSelectionLoading: memberSelectionLoading,
    mText: mText,
    mImage: mImage,
    mAudio: mAudio,
    mOther: mOther
  },
  props: {
    to: {
      type: Object,
      default: {}
    },
    userInfo: {
      type: Object,
      default: {}
    },
    userList: {
      type: Array,
      default: function _default() {
        return [];
      }
    },
    isPrivate: {
      type: Boolean,
      default: false
    }
  },
  data: function data() {
    return {
      a: 'data:image/svg+xml;base64,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',
      a_b: 'data:image/svg+xml;base64,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',
      b: 'data:image/svg+xml;base64,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',
      c: 'data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTUxMS4zIDk1OS43Yy02MC40IDAtMTE5LTExLjgtMTc0LjItMzUuMi01My4zLTIyLjUtMTAxLjEtNTQuOC0xNDIuMi05NS45LTQxLjEtNDEuMS03My40LTg4LjktOTUuOS0xNDIuMi0yMy4zLTU1LjItMzUuMi0xMTMuOC0zNS4yLTE3NC4yUzc1LjYgMzkzLjEgOTkgMzM4YzIyLjUtNTMuMyA1NC44LTEwMS4xIDk1LjktMTQyLjIgNDEuMS00MS4xIDg4LjktNzMuNCAxNDIuMi05NS45IDU1LjItMjMuMyAxMTMuOC0zNS4yIDE3NC4yLTM1LjIgNjAuNCAwIDExOSAxMS44IDE3NC4yIDM1LjIgNTMuMyAyMi41IDEwMS4xIDU0LjggMTQyLjIgOTUuOSA0MS4xIDQxLjEgNzMuNCA4OC45IDk1LjkgMTQyLjIgMjMuMyA1NS4yIDM1LjIgMTEzLjggMzUuMiAxNzQuMnMtMTEuOCAxMTktMzUuMiAxNzQuMmMtMjIuNSA1My4zLTU0LjggMTAxLjEtOTUuOSAxNDIuMi00MS4xIDQxLjEtODguOSA3My40LTE0Mi4yIDk1LjktNTUuMiAyMy4zLTExMy44IDM1LjItMTc0LjIgMzUuMnptMC04MzljLTUyLjkgMC0xMDQuMSAxMC4zLTE1Mi40IDMwLjgtNDYuNiAxOS43LTg4LjUgNDcuOS0xMjQuNSA4My45LTM2IDM2LTY0LjIgNzcuOC04My45IDEyNC41LTIwLjQgNDguMi0zMC44IDk5LjUtMzAuOCAxNTIuNHMxMC4zIDEwNC4xIDMwLjggMTUyLjRjMTkuNyA0Ni42IDQ3LjkgODguNSA4My45IDEyNC41IDM2IDM2IDc3LjggNjQuMiAxMjQuNSA4My45IDQ4LjIgMjAuNCA5OS41IDMwLjggMTUyLjQgMzAuOCA1Mi45IDAgMTA0LjEtMTAuMyAxNTIuNC0zMC44IDQ2LjYtMTkuNyA4OC41LTQ3LjkgMTI0LjUtODMuOXM2NC4yLTc3LjggODMuOS0xMjQuNWMyMC40LTQ4LjIgMzAuOC05OS41IDMwLjgtMTUyLjRTODkyLjQgNDA4IDg3MiAzNTkuOGMtMTkuNy00Ni42LTQ3LjktODguNS04My45LTEyNC41cy03Ny44LTY0LjItMTI0LjUtODMuOWMtNDguMi0yMC40LTk5LjUtMzAuNy0xNTIuMy0zMC43eiIvPjxwYXRoIGQ9Ik03MzcuMyA0ODQuMmgtMTk4di0xOThjMC0xNS41LTEyLjUtMjgtMjgtMjhzLTI4IDEyLjUtMjggMjh2MTk4aC0xOThjLTE1LjUgMC0yOCAxMi41LTI4IDI4czEyLjUgMjggMjggMjhoMTk4djE5OGMwIDE1LjUgMTIuNSAyOCAyOCAyOHMyOC0xMi41IDI4LTI4di0xOThoMTk4YzE1LjUgMCAyOC0xMi41IDI4LTI4cy0xMi42LTI4LTI4LTI4eiIvPjwvc3ZnPg==',
      isFocus: false,
      //键盘焦点
      isKeyboard: true,
      isEmoji: false,
      isMore: false,
      isRecorder: false,
      isCancel: false,
      //是否滑动到取消
      text: '',
      keyboardHeight: 0,
      isQuote: false,
      //是否引用
      quoteSource: {},
      //引用的源
      keyHeight: 0
    };
  },
  created: function created() {
    var _this = this;
    this.initRecorderListeners();
    // 监听设置群公告
    uni.$off('getNoticeSendMessage', this.sendMessage);
    uni.$on('getNoticeSendMessage', this.sendMessage);

    // 监听修改群明
    uni.$off('getGroupNameMessage', this.sendMessage);
    uni.$on('getGroupNameMessage', this.sendMessage);
    uni.onKeyboardHeightChange(function (res) {
      console.log('res.height', res.height);
      _this.keyHeight = res.height;
    });
  },
  beforeDestroy: function beforeDestroy() {
    uni.$off('getNoticeSendMessage', this.sendMessage);
    uni.$off('getGroupNameMessage', this.sendMessage);
    cursor = 0;
    clearInterval(getSelectedTextRangeSetInterval);
  },
  methods: {
    setCursor: function setCursor() {
      getSelectedTextRangeSetInterval = setInterval(function () {
        uni.getSelectedTextRange({
          success: function success(res) {
            cursor = res.start;
          },
          fail: function fail() {
            clearInterval(getSelectedTextRangeSetInterval);
          }
        });
      }, 800);
    },
    // 滚动到底部
    backToBottom: function backToBottom() {
      this.$emit('backToBottom');
    },
    // 关闭全部弹出/输入框/表情包
    closeAll: function closeAll() {
      this.isMore = false;
      this.isEmoji = false;
      this.isFocus = false;
    },
    onBottom: function onBottom() {
      this.$emit('onBottom');
    },
    // 重新编辑
    recalledEdit: function recalledEdit(item) {
      var _this2 = this;
      this.text = item.payload.text;
      this.$nextTick(function () {
        _this2.isFocus = true;
      });
    },
    // 关闭
    close: function close() {
      this.isMore = false;
      this.isEmoji = false;
      this.$emit('keyboardheightchange', 0);
    },
    // 切换语音输入
    onKeyboard: function onKeyboard() {
      this.isKeyboard = !this.isKeyboard;
      this.isMore = false;
      this.isEmoji = false;
    },
    keyboardheightchange: function keyboardheightchange(e) {
      var _this3 = this;
      if (e.detail.duration > 0) {
        this.backToBottom();
      }
      if (e.detail.duration > 0) {
        (0, _index.throttle)(function () {
          _this3.keyboardHeight = e.detail.height;
          _this3.isMore = false;
          _this3.isEmoji = false;
          _this3.$emit('keyboardheightchange', _this3.keyboardHeight);
        }, 300);
      }
    },
    tapEmoji: function tapEmoji() {
      this.backToBottom();
      this.isEmoji = !this.isEmoji;
      if (this.isEmoji) {
        this.isKeyboard = true;
      }
      this.isMore = false;
      this.$emit('keyboardheightchange', uni.upx2px(690));
    },
    tapMore: function tapMore() {
      this.backToBottom();
      this.isMore = !this.isMore;
      this.isEmoji = false;
      this.$emit('keyboardheightchange', uni.upx2px(430));
    },
    onEmoji: function onEmoji(key) {
      var text = "".concat(this.text.slice(0, cursor)).concat(key).concat(this.text.slice(cursor));
      this.text = text;
    },
    // ===========================
    // 获取焦点
    focus: function focus(e) {
      this.$emit('focus');
      this.isFocus = true;
      this.isEmoji = false;
      this.isMore = false;
      this.keyboardHeight = this.keyHeight;
      this.$emit('keyboardheightchange', this.keyboardHeight);
      this.backToBottom();
      clearInterval(getSelectedTextRangeSetInterval);
      this.setCursor();
    },
    handleBlur: function handleBlur() {
      this.isFocus = false;
      this.keyboardHeight = 0;
      this.$emit('keyboardheightchange', this.keyboardHeight);
      this.backToBottom();
    },
    // 监听输入,
    input: function input() {
      var _this4 = this;
      if (inputValue.length > this.text.length) {} else {
        var str = this.text.charAt(this.text.length - 1);
        if (str === '@') {
          if (this.isFocus === false) return;
          this.$refs.memberSelectionLoadingRef.open();
          this.$nextTick(function () {
            _this4.isFocus = false;
          });
        }
      }
      inputValue = this.text;
    },
    // 插入换行符合
    lineBreak: function lineBreak() {
      var _this5 = this;
      console.log('回车');
      console.log(cursor);
      var text = "".concat(this.text.slice(0, cursor), "\n").concat(this.text.slice(cursor));
      this.text = text;
      // this.text = `${this.text}\r\n`;
      this.$nextTick(function () {
        _this5.isFocus = true;
      });
    },
    // 输入@某个成员
    itemclick: function itemclick(item) {
      var _this6 = this;
      if (item) {
        this.text = "".concat(this.text).concat(item.name, " ");
      }
      this.$nextTick(function () {
        _this6.isFocus = true;
      });
    },
    // 删除表情
    deleteFn: function deleteFn() {
      var str = this.text.charAt(this.text.length - 1);
      if (str === ']') {
        var del = function del(str) {
          return text.slice(0, text.length - str.length);
        };
        var metaChars = /\[.*?(\u4e00*\u597d*)\]/g;
        var xstr = '';
        this.text.replace(metaChars, function (match) {
          xstr = match;
        });
        var text = this.text;
        this.text = del(xstr);
      } else {
        this.text = this.text.substring(0, this.text.length - 1);
      }
    },
    // 引用
    quote: function quote(item) {
      var _this7 = this;
      // 删除嵌套引用
      var itemx = JSON.parse(JSON.stringify(item));
      itemx.payload['quoteSource'] = {};
      this.isQuote = true;
      this.quoteSource = itemx;
      this.$nextTick(function () {
        _this7.isFocus = true;
      });
    },
    //谢谢红包
    thank: function thank(item) {
      this.text = '[彩带][玫瑰]谢谢红包！';
      this.sendingText();
    },
    //长按@某人
    mention: function mention(item) {
      var _this8 = this;
      this.text = "".concat(this.text, "@").concat(item.senderData.name, " ");
      this.$nextTick(function () {
        setTimeout(function () {
          _this8.isFocus = true;
        }, 500);
      });
      try {
        (0, _index.vibrateShortFn)();
      } catch (e) {
        //TODO handle the exception
      }
    },
    cancelQuote: function cancelQuote() {
      this.isQuote = false;
    },
    // 录音相关===============
    recorderTop: function recorderTop(e) {
      this.recorderTopValue = e === null || e === void 0 ? void 0 : e.top;
    },
    initRecorderListeners: function initRecorderListeners() {
      var _this9 = this;
      // 监听录音开始
      recorderManager.onStart(function () {
        // console.log('开始录音');
        startTime = Date.now();
      });
      //录音结束后，发送
      recorderManager.onStop(function (res) {
        _this9.isRecorder = false;
        if (_this9.isCancel) return console.log('取消发送'); //取消发送
        var endTime = Date.now();
        var duration = endTime - startTime;
        if (duration < 1000) return (0, _index.show)('录音时间太短', 1000, 'error');
        res.duration = duration;
        // 创建信息
        _this9.sendingRecorder(res);
      });
      // 监听录音报错
      recorderManager.onError(function (res) {
        _this9.isRecorder = false;
        recorderManager.stop();
        (0, _index.show)('请检查麦克风权限');
      });
    },
    // 按下
    touchstart: function touchstart() {
      this.isRecorder = true;
      this.isCancel = false;
      try {
        recorderManager.start();
      } catch (e) {
        (0, _index.show)('H5不支持');
      }
    },
    // 拖拽中
    touchmove: function touchmove(e) {
      var touch = e.touches[0]; //滑动过程中，手指滑动的坐标信息 返回的是Objcet对象
      if (touch.clientY <= this.recorderTopValue) {
        // 取消发送
        this.isCancel = true;
      } else {
        this.isCancel = false;
      }
    },
    // 松手
    touchend: function touchend() {
      try {
        recorderManager.stop();
      } catch (e) {
        console.log('e:', e);
      }
      this.isRecorder = false;
    },
    // ===================
    // 更多操作相关===============
    onMore: function onMore(item) {
      var _this10 = this;
      switch (item.type) {
        // 拍摄
        case 'shot':
          this.openShot();
          break;
        case 'img':
          this.sendImageMessage();
          break;
        case 'video':
          this.sendVideoMessage();
          break;
        case 'red_envelope':
          // 发红包
          uni.$off('send_red_envelope', this.sendMessage);
          uni.$on('send_red_envelope', this.sendMessage);

          // 是否是单聊
          if (this.isPrivate) {
            (0, _index.to)('/pagesGoEasy/envelope_sending/index-private', _objectSpread({}, this.to));
          } else {
            (0, _index.to)('/pagesGoEasy/envelope_sending/index', _objectSpread({}, this.to));
          }
          break;
        case 'mutualism':
          // 蝌蚪互转
          (0, _index.to)('/pagesThree/tadpoleChange/index?type=1');
          break;
        case 'map':
          // 位置
          uni.chooseLocation({
            success: function () {
              var _success = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee(res) {
                return _regenerator.default.wrap(function _callee$(_context) {
                  while (1) {
                    switch (_context.prev = _context.next) {
                      case 0:
                        console.log(res);
                        uni.showLoading({
                          title: '发送中'
                        });
                        if (res2) {
                          _this10.createCustomMessageMap(res, 'http://xxxxxxxx/map/staticMap?location=116.459044,39.918732&size=300*170');
                        }
                        uni.hideLoading();
                      case 4:
                      case "end":
                        return _context.stop();
                    }
                  }
                }, _callee);
              }));
              function success(_x) {
                return _success.apply(this, arguments);
              }
              return success;
            }(),
            fail: function fail(e) {
              console.log(e);
            }
          });
          break;
        default:
          break;
      }
    },
    openShot: function openShot() {
      var _this11 = this;
      (0, _index.show)('这个用的是原生插件，Html5App-CameraView');
      return;
      var plug = uni.requireNativePlugin('Html5App-CameraView');
      plug.open({
        setMaxduration: 30,
        SpeedColor: '#05c160',
        ratio: '9/16'
      }, function (retult) {
        var type = retult.type,
          _retult$mp = retult.mp4,
          mp4 = _retult$mp === void 0 ? '' : _retult$mp,
          _retult$duration = retult.duration,
          duration = _retult$duration === void 0 ? '' : _retult$duration,
          _retult$size = retult.size,
          size = _retult$size === void 0 ? '' : _retult$size,
          image = retult.image;
        if (type == 'video') {
          var file = {
            errMsg: 'chooseVideo:ok',
            tempFilePath: mp4,
            size: Number(size) * 1000,
            duration: duration,
            //视频时间
            width: 360,
            height: 640
          };
          _this11.createVideoMessage(file);
        } else if (type == 'image') {
          _this11.createImageMessage({
            size: Number(size) * 1000,
            path: image
          });
        }
        //用户取消拍摄
        if (retult.retult == 'cancel') {}
      });
    },
    // =====================
    // 创建发送输入框内容
    sendingText: function sendingText() {
      if (this.text === '') return uni.showModal({
        showCancel: false,
        content: '不能发送空白信息',
        success: function success(res) {}
      });
      var body = this.text;
      if (this.text.length >= 50) {
        body = this.text.substring(0, 30) + '...';
      }
      if (this.isQuote) {
        this.createCustomMessageText(body);
        return;
      }
      this.sendMessage({
        payload: {
          text: this.text
        },
        type: 'text'
      });
      this.text = '';
    },
    // 发送位置信息
    createCustomMessageMap: function createCustomMessageMap(res, image) {
      var latitude = res.latitude,
        longitude = res.longitude,
        address = res.address,
        name = res.name;
      this.sendMessage({
        payload: {
          latitude: latitude,
          longitude: longitude,
          title: name,
          address: address,
          image: image //使用高德api生成图片
        },

        type: 'map'
      });
    },
    // 引用并发送文本
    createCustomMessageText: function createCustomMessageText(body) {
      this.sendMessage({
        payload: {
          text: this.text,
          //引用源
          quoteSource: _objectSpread({}, this.quoteSource)
        },
        type: 'text_quote'
      });
      this.text = '';
    },
    // 创建发送照片内容
    sendImageMessage: function sendImageMessage() {
      var _this12 = this;
      uni.chooseImage({
        count: 9,
        success: function () {
          var _success2 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2(res) {
            return _regenerator.default.wrap(function _callee2$(_context2) {
              while (1) {
                switch (_context2.prev = _context2.next) {
                  case 0:
                    res.tempFiles.forEach(function (file) {
                      console.log(file);
                      _this12.createImageMessage(file);
                    });
                  case 1:
                  case "end":
                    return _context2.stop();
                }
              }
            }, _callee2);
          }));
          function success(_x2) {
            return _success2.apply(this, arguments);
          }
          return success;
        }()
      });
    },
    // 创建发送照片内容
    createImageMessage: function createImageMessage(file) {
      this.sendMessage({
        payload: {
          contentType: 'image/png',
          name: 'uni-image.png',
          size: 82942,
          url: file.path,
          width: 2732,
          height: 2732,
          thumbnail: file.path
        },
        type: 'image'
      });
    },
    // 创建发送视频内容
    sendVideoMessage: function sendVideoMessage() {
      var _this13 = this;
      uni.chooseVideo({
        success: function () {
          var _success3 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3(res) {
            return _regenerator.default.wrap(function _callee3$(_context3) {
              while (1) {
                switch (_context3.prev = _context3.next) {
                  case 0:
                    console.log(res);
                    _this13.createVideoMessage(res);
                  case 2:
                  case "end":
                    return _context3.stop();
                }
              }
            }, _callee3);
          }));
          function success(_x3) {
            return _success3.apply(this, arguments);
          }
          return success;
        }()
      });
    },
    createVideoMessage: function createVideoMessage(file) {
      this.sendMessage({
        payload: {
          video: {
            name: '3003009356267921_uni-video.mp4',
            url: file.tempFilePath,
            width: 640,
            height: 352,
            contentType: 'video/mp4',
            size: 501774,
            duration: 8.32
          },
          thumbnail: {
            name: 'uni-thumbnail.jpg',
            url: '封面路径',
            width: 364,
            height: 200,
            contentType: 'image/jpg'
          }
        },
        type: 'video'
      });
    },
    // 创建语音内容
    sendingRecorder: function sendingRecorder(file) {
      this.sendMessage({
        payload: {
          contentType: 'audio/mp3',
          name: 'uni-audio.mp3',
          size: 2357,
          url: file.tempFilePath,
          duration: 1.148
        },
        type: 'audio'
      });
    },
    // 创建自定义表情包
    sendingEmojiPack: function sendingEmojiPack(e) {
      this.sendMessage({
        payload: {
          ext: e.ext,
          url: e.url,
          path: e.path,
          text: e.text || '[表情包]'
        },
        type: 'emoji_pack'
      });
    },
    // 最终提交发送
    sendMessage: function sendMessage(_ref) {
      var payload = _ref.payload,
        type = _ref.type;
      // 获取当前时间
      var now = new Date();
      var createTime = now.toISOString().slice(0, 19).replace('T', ' ');
      // 生成消息ID
      var messageId = "".concat(this.to.id, "_").concat(Date.now().toString().slice(-6));
      var message = {
        content: payload.text || payload.url || payload.title || '',
        createBy: null,
        createTime: createTime,
        groupId: this.to.id,
        id: messageId,
        msgType: type,
        payload: payload,
        senderData: {
          avatar: this.userInfo.avatar || '',
          group_id: this.to.id,
          member_id: this.userInfo.userId,
          name: this.userInfo.name || this.userInfo.nickname || ''
        },
        status: '正常',
        sysOrgCode: null,
        updateBy: null,
        updateTime: null,
        userId: this.userInfo.userId
      };
      this.$emit('pushList', message);
      this.isQuote = false;
      this.quoteSource = {};
    }
  }
};
exports.default = _default2;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 247:
/*!************************************************************************************************************************************************************************!*\
  !*** D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/bottom-operation/index.vue?vue&type=style&index=0&id=0c5cade1&lang=scss&scoped=true& ***!
  \************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_0c5cade1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src??ref--8-oneOf-1-3!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!../../../../../../../../../../Development/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=0c5cade1&lang=scss&scoped=true& */ 248);
/* harmony import */ var _Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_0c5cade1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_0c5cade1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_0c5cade1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_0c5cade1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_Development_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_Development_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_Development_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_Development_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_0c5cade1_lang_scss_scoped_true___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 248:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!D:/Workspace/public/2025/IM-消息系统/im-best/pagesHuYunIm/pages/chat/components/bottom-operation/index.vue?vue&type=style&index=0&id=0c5cade1&lang=scss&scoped=true& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

}]);
//# sourceMappingURL=../../../../../../.sourcemap/mp-weixin/pagesHuYunIm/pages/chat/components/bottom-operation/index.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pagesHuYunIm/pages/chat/components/bottom-operation/index-create-component',
    {
        'pagesHuYunIm/pages/chat/components/bottom-operation/index-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(242))
        })
    },
    [['pagesHuYunIm/pages/chat/components/bottom-operation/index-create-component']]
]);
